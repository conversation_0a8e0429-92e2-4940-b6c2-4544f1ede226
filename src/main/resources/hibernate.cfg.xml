<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-configuration PUBLIC
        "-//Hibernate/Hibernate Configuration DTD 3.0//EN"
        "http://hibernate.sourceforge.net/hibernate-configuration-3.0.dtd">
<hibernate-configuration>
    <session-factory>
        <property name="hibernate.connection.driver_class">com.mysql.cj.jdbc.Driver</property>
        <property name="hibernate.connection.url">*********************************************************************</property>
        <property name="hibernate.connection.username">root</property>
        <property name="hibernate.connection.password">jatski</property>
        <property name="hibernate.dialect">org.hibernate.dialect.MySQL8Dialect</property>
        <property name="hibernate.hbm2ddl.auto">update</property>
        <property name="show_sql">true</property>
        <!-- Mapping des entités -->
        <mapping class="model.Vehicule"/>
        <mapping class="model.Client"/>
        <mapping class="model.Location"/>
        <mapping class="model.Paiement"/>
        <mapping class="model.Admin"/>
        <mapping class="model.Agent"/>
        <!-- Nouvelles entités pour fonctionnalités avancées -->
        <mapping class="model.Prolongation"/>
        <mapping class="model.HistoriqueVehicule"/>
        <mapping class="model.Maintenance"/>
        <mapping class="model.Accident"/>
    </session-factory>
</hibernate-configuration> 