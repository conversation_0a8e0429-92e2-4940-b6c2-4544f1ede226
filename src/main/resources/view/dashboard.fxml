<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.shape.*?>

<BorderPane prefHeight="800.0" prefWidth="1400.0" style="-fx-background-color: #f8fafc;" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.DashboardController">

    <!-- MODERN SIDEBAR START -->
    <left>
        <VBox prefWidth="260.0" spacing="20" style="-fx-background-color: #f1f5f9; -fx-padding: 32 16 32 16; -fx-background-radius: 0 24 24 0; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.04), 8, 0, 2, 0);">

            <!-- Logo -->
            <HBox alignment="CENTER_LEFT" spacing="10">
                <StackPane>
                    <Circle fill="#4f46e5" radius="20" />
                    <Label style="-fx-text-fill: white; -fx-font-size: 14px; -fx-font-weight: bold;" text="LV" />
                </StackPane>
                <VBox spacing="2">
                    <Label style="-fx-text-fill: #1e293b; -fx-font-size: 18px; -fx-font-weight: bold;" text="LocationV1" />
                    <Label style="-fx-text-fill: #64748b; -fx-font-size: 12px;" text="Administration" />
                </VBox>
            </HBox>

            <!-- GESTION HEADER -->
            <Label style="-fx-text-fill: #64748b; -fx-font-size: 12px; -fx-font-weight: bold; -fx-padding: 16 0 0 0;" text="GESTION" />

            <!-- Navigation Buttons -->
            <VBox spacing="8">
                <Button fx:id="btnDashboard" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" style="-fx-background-color: #2563eb; -fx-background-radius: 10; -fx-padding: 10 16; -fx-text-fill: white; -fx-font-weight: bold;">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="10">
                            <Label style="-fx-text-fill: #fff; -fx-font-size: 16px;" text="📊" />
                            <Label style="-fx-text-fill: white; -fx-font-size: 14px;" text="Tableau de bord" />
                        </HBox>
                    </graphic>
                </Button>
                <Button fx:id="btnClients" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" style="-fx-background-color: transparent; -fx-background-radius: 10; -fx-padding: 10 16; -fx-text-fill: #334155;">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="10">
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 16px;" text="👥" />
                            <Label style="-fx-text-fill: #334155; -fx-font-size: 14px;" text="Clients" />
                        </HBox>
                    </graphic>
                </Button>
                <Button fx:id="btnVehicules" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" style="-fx-background-color: transparent; -fx-background-radius: 10; -fx-padding: 10 16; -fx-text-fill: #334155;">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="10">
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 16px;" text="🚗" />
                            <Label style="-fx-text-fill: #334155; -fx-font-size: 14px;" text="Véhicules" />
                        </HBox>
                    </graphic>
                </Button>
                <Button fx:id="btnCatalogue" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" style="-fx-background-color: transparent; -fx-background-radius: 10; -fx-padding: 10 16; -fx-text-fill: #334155;">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="10">
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 16px;" text="📋" />
                            <Label style="-fx-text-fill: #334155; -fx-font-size: 14px;" text="Catalogue" />
                        </HBox>
                    </graphic>
                </Button>
                <Button fx:id="btnNewLocation" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" style="-fx-background-color: #f59e42; -fx-background-radius: 10; -fx-padding: 10 16; -fx-text-fill: white; -fx-font-weight: bold;">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="10">
                            <Label style="-fx-text-fill: #fff; -fx-font-size: 16px;" text="🆕" />
                            <Label style="-fx-text-fill: white; -fx-font-size: 14px;" text="Nouvelle Location" />
                        </HBox>
                    </graphic>
                </Button>
                <Button fx:id="btnLocations" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" style="-fx-background-color: transparent; -fx-background-radius: 10; -fx-padding: 10 16; -fx-text-fill: #334155;">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="10">
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 16px;" text="📝" />
                            <Label style="-fx-text-fill: #334155; -fx-font-size: 14px;" text="Locations" />
                        </HBox>
                    </graphic>
                </Button>
                <Button fx:id="btnPaiements" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" style="-fx-background-color: transparent; -fx-background-radius: 10; -fx-padding: 10 16; -fx-text-fill: #334155;">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="10">
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 16px;" text="💰" />
                            <Label style="-fx-text-fill: #334155; -fx-font-size: 14px;" text="Paiements" />
                        </HBox>
                    </graphic>
                </Button>
                <Button fx:id="btnUserManagement" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" style="-fx-background-color: transparent; -fx-background-radius: 10; -fx-padding: 10 16; -fx-text-fill: #334155;">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="10">
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 16px;" text="🔑" />
                            <Label style="-fx-text-fill: #334155; -fx-font-size: 14px;" text="Utilisateurs" />
                        </HBox>
                    </graphic>
                </Button>
                <!-- New buttons for rental and payment history -->
                <Button fx:id="btnRentalHistory" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" style="-fx-background-color: transparent; -fx-background-radius: 10; -fx-padding: 10 16; -fx-text-fill: #334155;">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="10">
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 16px;" text="📅" />
                            <Label style="-fx-text-fill: #334155; -fx-font-size: 14px;" text="Historique Locations" />
                        </HBox>
                    </graphic>
                </Button>
                <Button fx:id="btnPaymentHistory" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToPage" style="-fx-background-color: transparent; -fx-background-radius: 10; -fx-padding: 10 16; -fx-text-fill: #334155;">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="10">
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 16px;" text="💳" />
                            <Label style="-fx-text-fill: #334155; -fx-font-size: 14px;" text="Historique Paiements" />
                        </HBox>
                    </graphic>
                </Button>

                <!-- Nouvelles fonctionnalités -->
                <Button fx:id="btnProlongations" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToNewPage" style="-fx-background-color: transparent; -fx-background-radius: 10; -fx-padding: 10 16; -fx-text-fill: #334155;" userData="prolongation">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="10">
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 16px;" text="⏰" />
                            <Label style="-fx-text-fill: #334155; -fx-font-size: 14px;" text="Prolongations" />
                        </HBox>
                    </graphic>
                </Button>

                <Button fx:id="btnMaintenance" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToNewPage" style="-fx-background-color: transparent; -fx-background-radius: 10; -fx-padding: 10 16; -fx-text-fill: #334155;" userData="maintenance">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="10">
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 16px;" text="🔧" />
                            <Label style="-fx-text-fill: #334155; -fx-font-size: 14px;" text="Maintenance" />
                        </HBox>
                    </graphic>
                </Button>

                <Button fx:id="btnAccidents" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToNewPage" style="-fx-background-color: transparent; -fx-background-radius: 10; -fx-padding: 10 16; -fx-text-fill: #334155;" userData="accident">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="10">
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 16px;" text="🚨" />
                            <Label style="-fx-text-fill: #334155; -fx-font-size: 14px;" text="Accidents" />
                        </HBox>
                    </graphic>
                </Button>

                <Button fx:id="btnHistoriqueVehicule" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#navigateToNewPage" style="-fx-background-color: transparent; -fx-background-radius: 10; -fx-padding: 10 16; -fx-text-fill: #334155;" userData="historique_vehicule">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="10">
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 16px;" text="📋" />
                            <Label style="-fx-text-fill: #334155; -fx-font-size: 14px;" text="Historique Véhicules" />
                        </HBox>
                    </graphic>
                </Button>
            </VBox>

            <!-- COMPTE SECTION -->
            <VBox spacing="8">
                <Label style="-fx-text-fill: #64748b; -fx-font-size: 12px; -fx-font-weight: bold; -fx-padding: 16 0 0 0;" text="COMPTE" />
                <Button fx:id="btnLogout" alignment="CENTER_LEFT" maxWidth="Infinity" onAction="#handleLogout" style="-fx-background-color: transparent; -fx-background-radius: 10; -fx-padding: 10 16; -fx-text-fill: #334155;">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="10">
                            <Label style="-fx-text-fill: #64748b; -fx-font-size: 16px;" text="🚪" />
                            <Label style="-fx-text-fill: #334155; -fx-font-size: 14px;" text="Déconnexion" />
                        </HBox>
                    </graphic>
                </Button>
            </VBox>

            <!-- Spacer -->
            <Pane VBox.vgrow="ALWAYS" />

            <!-- USER PROFILE SECTION -->
            <HBox alignment="CENTER_LEFT" spacing="12" style="-fx-background-color: #2563eb; -fx-padding: 12 16; -fx-background-radius: 10;">
                <StackPane>
                    <Circle fill="#10b981" radius="20" />
                    <Label fx:id="lblUserName" style="-fx-text-fill: white; -fx-font-size: 13px; -fx-font-weight: bold;" text="AD" />
                </StackPane>
                <VBox spacing="2">
                    <Label style="-fx-text-fill: white; -fx-font-size: 14px; -fx-font-weight: bold;" text="Administrateur" />
                    <Label fx:id="lblUserRole" style="-fx-text-fill: #dbeafe; -fx-font-size: 12px;" text="<EMAIL>" />
                </VBox>
            </HBox>
        </VBox>
    </left>

    <!-- CENTER CONTENT (unchanged) -->
    <center>
        <StackPane fx:id="contentPane" style="-fx-background-color: #f8fafc;" />
    </center>
</BorderPane>
