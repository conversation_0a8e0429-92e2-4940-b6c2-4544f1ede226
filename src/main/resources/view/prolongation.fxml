<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.ProlongationController">
   <top>
      <!-- Header avec navigation -->
      <VBox>
         <MenuBar>
            <Menu text="Navigation">
               <MenuItem onAction="#navigateToPage" text="Tableau de Bord" userData="dashboard" />
               <MenuItem onAction="#navigateToPage" text="Clients" userData="client" />
               <MenuItem onAction="#navigateToPage" text="Véhicules" userData="vehicule" />
               <MenuItem onAction="#navigateToPage" text="Locations" userData="location" />
               <MenuItem onAction="#navigateToPage" text="Paiements" userData="paiement" />
               <MenuItem onAction="#navigateToPage" text="Maintenance" userData="maintenance" />
               <MenuItem onAction="#navigateToPage" text="Accidents" userData="accident" />
               <SeparatorMenuItem />
               <MenuItem onAction="#handleLogout" text="Déconnexion" />
            </Menu>
         </MenuBar>
         
         <!-- Titre et statistiques -->
         <HBox alignment="CENTER_LEFT" spacing="20.0" style="-fx-background-color: #2c3e50; -fx-padding: 15;">
            <Label text="Gestion des Prolongations" textFill="WHITE">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <VBox alignment="CENTER">
               <Label fx:id="lblTotalCount" text="Total: 0" textFill="WHITE" />
            </VBox>
            <VBox alignment="CENTER">
               <Label fx:id="lblEnAttente" text="En attente: 0" textFill="#f39c12" />
            </VBox>
            <VBox alignment="CENTER">
               <Label fx:id="lblApprouvees" text="Approuvées: 0" textFill="#27ae60" />
            </VBox>
            <VBox alignment="CENTER">
               <Label fx:id="lblRefusees" text="Refusées: 0" textFill="#e74c3c" />
            </VBox>
         </HBox>
      </VBox>
   </top>
   
   <center>
      <SplitPane dividerPositions="0.7" orientation="HORIZONTAL">
         <!-- Liste des prolongations -->
         <VBox spacing="10.0">
            <padding>
               <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
            </padding>
            
            <!-- Filtres et recherche -->
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <Label text="Recherche:" />
               <TextField fx:id="searchField" prefWidth="200.0" promptText="Rechercher..." />
               
               <Label text="Statut:" />
               <ComboBox fx:id="statutFilter" prefWidth="120.0" promptText="Tous" />
               
               <Label text="Du:" />
               <DatePicker fx:id="dateDebutFilter" prefWidth="120.0" />
               
               <Label text="Au:" />
               <DatePicker fx:id="dateFinFilter" prefWidth="120.0" />
               
               <Button onAction="#handleNouvelleProlongation" style="-fx-background-color: #27ae60; -fx-text-fill: white;" text="Nouvelle" />
            </HBox>
            
            <!-- Tableau des prolongations -->
            <TableView fx:id="prolongationTable" VBox.vgrow="ALWAYS">
               <columns>
                  <TableColumn fx:id="idColumn" prefWidth="50.0" text="ID" />
                  <TableColumn fx:id="locationColumn" prefWidth="80.0" text="Location" />
                  <TableColumn fx:id="clientColumn" prefWidth="150.0" text="Client" />
                  <TableColumn fx:id="vehiculeColumn" prefWidth="180.0" text="Véhicule" />
                  <TableColumn fx:id="ancienneDateColumn" prefWidth="100.0" text="Anc. Date" />
                  <TableColumn fx:id="nouvelleDateColumn" prefWidth="100.0" text="Nouv. Date" />
                  <TableColumn fx:id="dateDemandeeColumn" prefWidth="100.0" text="Demandée" />
                  <TableColumn fx:id="statutColumn" prefWidth="100.0" text="Statut" />
                  <TableColumn fx:id="prixColumn" prefWidth="80.0" text="Prix" />
                  <TableColumn fx:id="agentColumn" prefWidth="100.0" text="Agent" />
               </columns>
            </TableView>
            
            <!-- Boutons d'action -->
            <HBox alignment="CENTER" spacing="10.0">
               <Button onAction="#handleModifierProlongation" style="-fx-background-color: #3498db; -fx-text-fill: white;" text="Modifier" />
               <Button onAction="#handleApprouverProlongation" style="-fx-background-color: #27ae60; -fx-text-fill: white;" text="Approuver" />
               <Button onAction="#handleRefuserProlongation" style="-fx-background-color: #e74c3c; -fx-text-fill: white;" text="Refuser" />
            </HBox>
         </VBox>
         
         <!-- Formulaire de prolongation -->
         <VBox spacing="15.0" style="-fx-background-color: #ecf0f1;">
            <padding>
               <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
            </padding>
            
            <Label text="Formulaire de Prolongation" style="-fx-font-size: 18px; -fx-font-weight: bold;" />
            
            <GridPane hgap="10.0" vgap="15.0">
               <columnConstraints>
                  <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                  <ColumnConstraints hgrow="ALWAYS" />
               </columnConstraints>
               
               <!-- Informations de base -->
               <Label text="Location:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
               <ComboBox fx:id="locationComboBox" maxWidth="1.7976931348623157E308" GridPane.columnIndex="1" GridPane.rowIndex="0" />
               
               <Label text="Nouvelle date fin:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
               <DatePicker fx:id="nouvelleDateFinPicker" maxWidth="1.7976931348623157E308" GridPane.columnIndex="1" GridPane.rowIndex="1" />
               
               <Label text="Motif:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
               <TextField fx:id="motifField" maxWidth="1.7976931348623157E308" promptText="Motif de la prolongation..." GridPane.columnIndex="1" GridPane.rowIndex="2" />
               
               <Label text="Prix prolongation:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
               <TextField fx:id="prixProlongField" maxWidth="1.7976931348623157E308" promptText="0.00 DH" GridPane.columnIndex="1" GridPane.rowIndex="3" />
               
               <Label text="Kilométrage actuel:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
               <TextField fx:id="kilometrageField" maxWidth="1.7976931348623157E308" promptText="Kilométrage..." GridPane.columnIndex="1" GridPane.rowIndex="4" />
               
               <!-- Vérifications réglementaires -->
               <Label text="Vérifications:" GridPane.columnIndex="0" GridPane.rowIndex="5" />
               <VBox spacing="5.0" GridPane.columnIndex="1" GridPane.rowIndex="5">
                  <CheckBox fx:id="verificationPermisCheck" text="Permis valide vérifié" />
                  <CheckBox fx:id="verificationAssuranceCheck" text="Assurance valide vérifiée" />
                  <CheckBox fx:id="controleVehiculeCheck" text="Contrôle véhicule effectué" />
               </VBox>
               
               <!-- Notes -->
               <Label text="Notes agent:" GridPane.columnIndex="0" GridPane.rowIndex="6" />
               <TextArea fx:id="notesArea" maxWidth="1.7976931348623157E308" prefRowCount="3" promptText="Notes et observations..." GridPane.columnIndex="1" GridPane.rowIndex="6" />
            </GridPane>
            
            <!-- Boutons de sauvegarde -->
            <HBox alignment="CENTER" spacing="10.0">
               <Button onAction="#handleSauvegarderProlongation" style="-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-size: 14px;" text="Sauvegarder" />
               <Button onAction="#handleNouvelleProlongation" style="-fx-background-color: #95a5a6; -fx-text-fill: white;" text="Nouveau" />
            </HBox>
         </VBox>
      </SplitPane>
   </center>
</BorderPane>
