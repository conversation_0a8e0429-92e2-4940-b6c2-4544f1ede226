<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.HistoriqueVehiculeController">
   <top>
      <!-- Header avec navigation -->
      <VBox>
         <MenuBar>
            <Menu text="Navigation">
               <MenuItem onAction="#navigateToPage" text="Tableau de Bord" userData="dashboard" />
               <MenuItem onAction="#navigateToPage" text="Clients" userData="client" />
               <MenuItem onAction="#navigateToPage" text="Véhicules" userData="vehicule" />
               <MenuItem onAction="#navigateToPage" text="Locations" userData="location" />
               <MenuItem onAction="#navigateToPage" text="Paiements" userData="paiement" />
               <MenuItem onAction="#navigateToPage" text="Prolongations" userData="prolongation" />
               <MenuItem onAction="#navigateToPage" text="Maintenance" userData="maintenance" />
               <MenuItem onAction="#navigateToPage" text="Accidents" userData="accident" />
               <SeparatorMenuItem />
               <MenuItem onAction="#handleLogout" text="Déconnexion" />
            </Menu>
         </MenuBar>
         
         <!-- Titre et statistiques -->
         <HBox alignment="CENTER_LEFT" spacing="20.0" style="-fx-background-color: #8e44ad; -fx-padding: 15;">
            <Label text="Historique des Véhicules" textFill="WHITE">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <VBox alignment="CENTER">
               <Label fx:id="lblTotalEvenements" text="Événements: 0" textFill="WHITE" />
            </VBox>
            <VBox alignment="CENTER">
               <Label fx:id="lblVehiculesConcernes" text="Véhicules: 0" textFill="#ecf0f1" />
            </VBox>
            <VBox alignment="CENTER">
               <Label fx:id="lblCoutTotal" text="Coût total: 0 DH" textFill="#f39c12" />
            </VBox>
         </HBox>
      </VBox>
   </top>
   
   <center>
      <SplitPane dividerPositions="0.75" orientation="HORIZONTAL">
         <!-- Liste de l'historique -->
         <VBox spacing="10.0">
            <padding>
               <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
            </padding>
            
            <!-- Filtres et recherche -->
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <TextField fx:id="searchField" prefWidth="150.0" promptText="Rechercher..." />
               
               <ComboBox fx:id="vehiculeFilter" prefWidth="150.0" promptText="Véhicule" />
               <ComboBox fx:id="typeEvenementFilter" prefWidth="120.0" promptText="Type" />
               
               <DatePicker fx:id="dateDebutFilter" prefWidth="110.0" />
               <DatePicker fx:id="dateFinFilter" prefWidth="110.0" />
               
               <CheckBox fx:id="evenementsFinanciersCheck" text="Avec coût" />
               
               <Button onAction="#handleExporterHistorique" style="-fx-background-color: #27ae60; -fx-text-fill: white;" text="Exporter" />
            </HBox>
            
            <!-- Tableau de l'historique -->
            <TableView fx:id="historiqueTable" VBox.vgrow="ALWAYS">
               <columns>
                  <TableColumn fx:id="idColumn" prefWidth="40.0" text="ID" />
                  <TableColumn fx:id="vehiculeColumn" prefWidth="120.0" text="Véhicule" />
                  <TableColumn fx:id="typeColumn" prefWidth="120.0" text="Type" />
                  <TableColumn fx:id="dateColumn" prefWidth="100.0" text="Date" />
                  <TableColumn fx:id="descriptionColumn" prefWidth="200.0" text="Description" />
                  <TableColumn fx:id="clientColumn" prefWidth="120.0" text="Client" />
                  <TableColumn fx:id="kilometrageColumn" prefWidth="80.0" text="Km" />
                  <TableColumn fx:id="coutColumn" prefWidth="80.0" text="Coût" />
                  <TableColumn fx:id="fournisseurColumn" prefWidth="120.0" text="Fournisseur" />
               </columns>
            </TableView>
            
            <!-- Statistiques par type -->
            <HBox alignment="CENTER" spacing="20.0" style="-fx-background-color: #ecf0f1; -fx-padding: 10;">
               <VBox alignment="CENTER">
                  <Label fx:id="lblAcquisitions" text="Acquisitions: 0" style="-fx-font-weight: bold;" />
               </VBox>
               <VBox alignment="CENTER">
                  <Label fx:id="lblLocations" text="Locations: 0" style="-fx-font-weight: bold;" />
               </VBox>
               <VBox alignment="CENTER">
                  <Label fx:id="lblMaintenances" text="Maintenances: 0" style="-fx-font-weight: bold;" />
               </VBox>
               <VBox alignment="CENTER">
                  <Label fx:id="lblAccidents" text="Accidents: 0" style="-fx-font-weight: bold;" />
               </VBox>
            </HBox>
         </VBox>
         
         <!-- Panneau de détails -->
         <VBox spacing="15.0" style="-fx-background-color: #ecf0f1;">
            <padding>
               <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
            </padding>
            
            <Label text="Détails de l'Événement" style="-fx-font-size: 18px; -fx-font-weight: bold;" />
            
            <ScrollPane fitToWidth="true" VBox.vgrow="ALWAYS">
               <VBox spacing="10.0">
                  <!-- Informations générales -->
                  <TitledPane text="Informations Générales" expanded="true">
                     <GridPane hgap="10.0" vgap="10.0">
                        <columnConstraints>
                           <ColumnConstraints hgrow="NEVER" minWidth="100.0" />
                           <ColumnConstraints hgrow="ALWAYS" />
                        </columnConstraints>
                        
                        <Label text="ID:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <Label fx:id="detailIdLabel" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <Label text="Véhicule:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <Label fx:id="detailVehiculeLabel" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <Label text="Type:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <Label fx:id="detailTypeLabel" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                        
                        <Label text="Date:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                        <Label fx:id="detailDateLabel" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                        
                        <Label text="Agent:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                        <Label fx:id="detailAgentLabel" GridPane.columnIndex="1" GridPane.rowIndex="4" />
                     </GridPane>
                  </TitledPane>
                  
                  <!-- Description -->
                  <TitledPane text="Description" expanded="true">
                     <VBox spacing="5.0">
                        <Label fx:id="detailDescriptionLabel" wrapText="true" />
                        <Separator />
                        <Label fx:id="detailDetailsLabel" wrapText="true" style="-fx-text-fill: #7f8c8d;" />
                     </VBox>
                  </TitledPane>
                  
                  <!-- Informations kilométrage -->
                  <TitledPane text="Kilométrage" expanded="false">
                     <GridPane hgap="10.0" vgap="10.0">
                        <columnConstraints>
                           <ColumnConstraints hgrow="NEVER" minWidth="100.0" />
                           <ColumnConstraints hgrow="ALWAYS" />
                        </columnConstraints>
                        
                        <Label text="Avant:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <Label fx:id="detailKmAvantLabel" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <Label text="Après:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <Label fx:id="detailKmApresLabel" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <Label text="Parcouru:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <Label fx:id="detailKmParcouruLabel" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                     </GridPane>
                  </TitledPane>
                  
                  <!-- Informations financières -->
                  <TitledPane text="Informations Financières" expanded="false">
                     <GridPane hgap="10.0" vgap="10.0">
                        <columnConstraints>
                           <ColumnConstraints hgrow="NEVER" minWidth="100.0" />
                           <ColumnConstraints hgrow="ALWAYS" />
                        </columnConstraints>
                        
                        <Label text="Coût:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <Label fx:id="detailCoutLabel" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <Label text="Fournisseur:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <Label fx:id="detailFournisseurLabel" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <Label text="N° Facture:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <Label fx:id="detailFactureLabel" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                        
                        <Label text="N° Rapport:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                        <Label fx:id="detailRapportLabel" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                     </GridPane>
                  </TitledPane>
                  
                  <!-- Client et location -->
                  <TitledPane text="Client et Location" expanded="false">
                     <GridPane hgap="10.0" vgap="10.0">
                        <columnConstraints>
                           <ColumnConstraints hgrow="NEVER" minWidth="100.0" />
                           <ColumnConstraints hgrow="ALWAYS" />
                        </columnConstraints>
                        
                        <Label text="Client:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <Label fx:id="detailClientLabel" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <Label text="Location:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <Label fx:id="detailLocationLabel" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                     </GridPane>
                  </TitledPane>
                  
                  <!-- Observations -->
                  <TitledPane text="Observations" expanded="false">
                     <TextArea fx:id="detailObservationsArea" editable="false" prefRowCount="4" wrapText="true" />
                  </TitledPane>
               </VBox>
            </ScrollPane>
         </VBox>
      </SplitPane>
   </center>
</BorderPane>
