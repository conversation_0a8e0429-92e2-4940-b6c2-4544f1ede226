<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.MaintenanceController">
   <top>
      <!-- Header avec navigation -->
      <VBox>
         <MenuBar>
            <Menu text="Navigation">
               <MenuItem onAction="#navigateToPage" text="Tableau de Bord" userData="dashboard" />
               <MenuItem onAction="#navigateToPage" text="Clients" userData="client" />
               <MenuItem onAction="#navigateToPage" text="Véhicules" userData="vehicule" />
               <MenuItem onAction="#navigateToPage" text="Locations" userData="location" />
               <MenuItem onAction="#navigateToPage" text="Paiements" userData="paiement" />
               <MenuItem onAction="#navigateToPage" text="Prolongations" userData="prolongation" />
               <MenuItem onAction="#navigateToPage" text="Accidents" userData="accident" />
               <SeparatorMenuItem />
               <MenuItem onAction="#handleLogout" text="Déconnexion" />
            </Menu>
         </MenuBar>
         
         <!-- Titre et statistiques -->
         <HBox alignment="CENTER_LEFT" spacing="20.0" style="-fx-background-color: #34495e; -fx-padding: 15;">
            <Label text="Gestion de la Maintenance" textFill="WHITE">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <VBox alignment="CENTER">
               <Label fx:id="lblTotalCount" text="Total: 0" textFill="WHITE" />
            </VBox>
            <VBox alignment="CENTER">
               <Label fx:id="lblProgrammees" text="Programmées: 0" textFill="#3498db" />
            </VBox>
            <VBox alignment="CENTER">
               <Label fx:id="lblEnCours" text="En cours: 0" textFill="#f39c12" />
            </VBox>
            <VBox alignment="CENTER">
               <Label fx:id="lblTerminees" text="Terminées: 0" textFill="#27ae60" />
            </VBox>
            <VBox alignment="CENTER">
               <Label fx:id="lblUrgentes" text="Urgentes: 0" textFill="#e74c3c" />
            </VBox>
         </HBox>
      </VBox>
   </top>
   
   <center>
      <SplitPane dividerPositions="0.65" orientation="HORIZONTAL">
         <!-- Liste des maintenances -->
         <VBox spacing="10.0">
            <padding>
               <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
            </padding>
            
            <!-- Filtres et recherche -->
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <TextField fx:id="searchField" prefWidth="150.0" promptText="Rechercher..." />
               
               <ComboBox fx:id="statutFilter" prefWidth="100.0" promptText="Statut" />
               <ComboBox fx:id="typeFilter" prefWidth="120.0" promptText="Type" />
               <ComboBox fx:id="prioriteFilter" prefWidth="100.0" promptText="Priorité" />
               
               <DatePicker fx:id="dateDebutFilter" prefWidth="110.0" />
               <DatePicker fx:id="dateFinFilter" prefWidth="110.0" />
               
               <Button onAction="#handleNouvelleMaintenance" style="-fx-background-color: #27ae60; -fx-text-fill: white;" text="Nouvelle" />
            </HBox>
            
            <!-- Tableau des maintenances -->
            <TableView fx:id="maintenanceTable" VBox.vgrow="ALWAYS">
               <columns>
                  <TableColumn fx:id="idColumn" prefWidth="40.0" text="ID" />
                  <TableColumn fx:id="vehiculeColumn" prefWidth="140.0" text="Véhicule" />
                  <TableColumn fx:id="typeColumn" prefWidth="100.0" text="Type" />
                  <TableColumn fx:id="statutColumn" prefWidth="90.0" text="Statut" />
                  <TableColumn fx:id="dateProgrammeeColumn" prefWidth="90.0" text="Date" />
                  <TableColumn fx:id="prioriteColumn" prefWidth="80.0" text="Priorité" />
                  <TableColumn fx:id="coutColumn" prefWidth="80.0" text="Coût" />
                  <TableColumn fx:id="garageColumn" prefWidth="120.0" text="Garage" />
               </columns>
            </TableView>
            
            <!-- Boutons d'action -->
            <HBox alignment="CENTER" spacing="10.0">
               <Button onAction="#handleModifierMaintenance" style="-fx-background-color: #3498db; -fx-text-fill: white;" text="Modifier" />
               <Button onAction="#handleCommencerMaintenance" style="-fx-background-color: #f39c12; -fx-text-fill: white;" text="Commencer" />
               <Button onAction="#handleTerminerMaintenance" style="-fx-background-color: #27ae60; -fx-text-fill: white;" text="Terminer" />
            </HBox>
         </VBox>
         
         <!-- Formulaire de maintenance -->
         <ScrollPane fitToWidth="true" style="-fx-background-color: #ecf0f1;">
            <VBox spacing="15.0">
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
               </padding>
               
               <Label text="Formulaire de Maintenance" style="-fx-font-size: 18px; -fx-font-weight: bold;" />
               
               <!-- Onglets pour organiser le formulaire -->
               <TabPane>
                  <!-- Onglet Informations générales -->
                  <Tab text="Général" closable="false">
                     <GridPane hgap="10.0" vgap="15.0">
                        <columnConstraints>
                           <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                           <ColumnConstraints hgrow="ALWAYS" />
                        </columnConstraints>
                        
                        <Label text="Véhicule:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <ComboBox fx:id="vehiculeComboBox" maxWidth="1.7976931348623157E308" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <Label text="Type:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <ComboBox fx:id="typeMaintenanceComboBox" maxWidth="1.7976931348623157E308" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <Label text="Priorité:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <ComboBox fx:id="prioriteComboBox" maxWidth="1.7976931348623157E308" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                        
                        <Label text="Date programmée:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                        <DatePicker fx:id="dateProgrammeePicker" maxWidth="1.7976931348623157E308" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                        
                        <Label text="Description:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                        <TextField fx:id="descriptionField" maxWidth="1.7976931348623157E308" promptText="Description de la maintenance..." GridPane.columnIndex="1" GridPane.rowIndex="4" />
                        
                        <Label text="Kilométrage:" GridPane.columnIndex="0" GridPane.rowIndex="5" />
                        <TextField fx:id="kilometrageField" maxWidth="1.7976931348623157E308" promptText="Kilométrage actuel..." GridPane.columnIndex="1" GridPane.rowIndex="5" />
                        
                        <Label text="Intervalle km:" GridPane.columnIndex="0" GridPane.rowIndex="6" />
                        <TextField fx:id="intervalleKmField" maxWidth="1.7976931348623157E308" promptText="Intervalle en km..." GridPane.columnIndex="1" GridPane.rowIndex="6" />
                        
                        <Label text="Durée estimée:" GridPane.columnIndex="0" GridPane.rowIndex="7" />
                        <TextField fx:id="dureeEstimeeField" maxWidth="1.7976931348623157E308" promptText="Durée en jours..." GridPane.columnIndex="1" GridPane.rowIndex="7" />
                        
                        <Label text="Véhicule immobilisé:" GridPane.columnIndex="0" GridPane.rowIndex="8" />
                        <CheckBox fx:id="vehiculeImmobiliseCheck" GridPane.columnIndex="1" GridPane.rowIndex="8" />
                        
                        <Label text="Observations avant:" GridPane.columnIndex="0" GridPane.rowIndex="9" />
                        <TextArea fx:id="observationsAvantArea" maxWidth="1.7976931348623157E308" prefRowCount="3" promptText="Observations avant maintenance..." GridPane.columnIndex="1" GridPane.rowIndex="9" />
                     </GridPane>
                  </Tab>
                  
                  <!-- Onglet Garage et technicien -->
                  <Tab text="Garage" closable="false">
                     <GridPane hgap="10.0" vgap="15.0">
                        <columnConstraints>
                           <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                           <ColumnConstraints hgrow="ALWAYS" />
                        </columnConstraints>
                        
                        <Label text="Nom garage:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <TextField fx:id="nomGarageField" maxWidth="1.7976931348623157E308" promptText="Nom du garage..." GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <Label text="Téléphone:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <TextField fx:id="telephoneGarageField" maxWidth="1.7976931348623157E308" promptText="Téléphone du garage..." GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <Label text="Adresse:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <TextArea fx:id="adresseGarageArea" maxWidth="1.7976931348623157E308" prefRowCount="2" promptText="Adresse du garage..." GridPane.columnIndex="1" GridPane.rowIndex="2" />
                        
                        <Label text="Technicien:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                        <TextField fx:id="technicienField" maxWidth="1.7976931348623157E308" promptText="Nom du technicien..." GridPane.columnIndex="1" GridPane.rowIndex="3" />
                     </GridPane>
                  </Tab>
                  
                  <!-- Onglet Résultats -->
                  <Tab text="Résultats" closable="false">
                     <GridPane hgap="10.0" vgap="15.0">
                        <columnConstraints>
                           <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                           <ColumnConstraints hgrow="ALWAYS" />
                        </columnConstraints>
                        
                        <Label text="Travaux effectués:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <TextArea fx:id="travauEffectuesArea" maxWidth="1.7976931348623157E308" prefRowCount="3" promptText="Détail des travaux effectués..." GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <Label text="Pièces changées:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <TextArea fx:id="piecesChangeesArea" maxWidth="1.7976931348623157E308" prefRowCount="3" promptText="Liste des pièces changées..." GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <Label text="Observations après:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <TextArea fx:id="observationsApresArea" maxWidth="1.7976931348623157E308" prefRowCount="3" promptText="Observations après maintenance..." GridPane.columnIndex="1" GridPane.rowIndex="2" />
                        
                        <Label text="Coût main d'œuvre:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                        <TextField fx:id="coutMainOeuvreField" maxWidth="1.7976931348623157E308" promptText="0.00 DH" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                        
                        <Label text="Coût pièces:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                        <TextField fx:id="coutPiecesField" maxWidth="1.7976931348623157E308" promptText="0.00 DH" GridPane.columnIndex="1" GridPane.rowIndex="4" />
                        
                        <Label text="N° facture:" GridPane.columnIndex="0" GridPane.rowIndex="5" />
                        <TextField fx:id="numeroFactureField" maxWidth="1.7976931348623157E308" promptText="Numéro de facture..." GridPane.columnIndex="1" GridPane.rowIndex="5" />
                        
                        <Label text="Facture payée:" GridPane.columnIndex="0" GridPane.rowIndex="6" />
                        <CheckBox fx:id="facturePayeeCheck" GridPane.columnIndex="1" GridPane.rowIndex="6" />
                        
                        <Label text="Conformité:" GridPane.columnIndex="0" GridPane.rowIndex="7" />
                        <CheckBox fx:id="conformiteCheck" text="Conforme à la réglementation" GridPane.columnIndex="1" GridPane.rowIndex="7" />
                     </GridPane>
                  </Tab>
               </TabPane>
               
               <!-- Boutons de sauvegarde -->
               <HBox alignment="CENTER" spacing="10.0">
                  <Button onAction="#handleSauvegarderMaintenance" style="-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-size: 14px;" text="Sauvegarder" />
                  <Button onAction="#handleNouvelleMaintenance" style="-fx-background-color: #95a5a6; -fx-text-fill: white;" text="Nouveau" />
               </HBox>
            </VBox>
         </ScrollPane>
      </SplitPane>
   </center>
</BorderPane>
