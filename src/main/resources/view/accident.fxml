<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="controller.AccidentController">
   <top>
      <!-- Header avec navigation -->
      <VBox>
         <MenuBar>
            <Menu text="Navigation">
               <MenuItem onAction="#navigateToPage" text="Tableau de Bord" userData="dashboard" />
               <MenuItem onAction="#navigateToPage" text="Clients" userData="client" />
               <MenuItem onAction="#navigateToPage" text="Véhicules" userData="vehicule" />
               <MenuItem onAction="#navigateToPage" text="Locations" userData="location" />
               <MenuItem onAction="#navigateToPage" text="Paiements" userData="paiement" />
               <MenuItem onAction="#navigateToPage" text="Prolongations" userData="prolongation" />
               <MenuItem onAction="#navigateToPage" text="Maintenance" userData="maintenance" />
               <SeparatorMenuItem />
               <MenuItem onAction="#handleLogout" text="Déconnexion" />
            </Menu>
         </MenuBar>
         
         <!-- Titre et statistiques -->
         <HBox alignment="CENTER_LEFT" spacing="20.0" style="-fx-background-color: #c0392b; -fx-padding: 15;">
            <Label text="Gestion des Accidents" textFill="WHITE">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <VBox alignment="CENTER">
               <Label fx:id="lblTotalCount" text="Total: 0" textFill="WHITE" />
            </VBox>
            <VBox alignment="CENTER">
               <Label fx:id="lblAccidentsGraves" text="Graves: 0" textFill="#f39c12" />
            </VBox>
            <VBox alignment="CENTER">
               <Label fx:id="lblEnCours" text="En cours: 0" textFill="#3498db" />
            </VBox>
            <VBox alignment="CENTER">
               <Label fx:id="lblResolus" text="Résolus: 0" textFill="#27ae60" />
            </VBox>
         </HBox>
      </VBox>
   </top>
   
   <center>
      <SplitPane dividerPositions="0.6" orientation="HORIZONTAL">
         <!-- Liste des accidents -->
         <VBox spacing="10.0">
            <padding>
               <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
            </padding>
            
            <!-- Filtres et recherche -->
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <TextField fx:id="searchField" prefWidth="150.0" promptText="Rechercher..." />
               
               <ComboBox fx:id="typeFilter" prefWidth="120.0" promptText="Type" />
               <ComboBox fx:id="graviteFilter" prefWidth="100.0" promptText="Gravité" />
               <ComboBox fx:id="statutFilter" prefWidth="100.0" promptText="Statut" />
               
               <DatePicker fx:id="dateDebutFilter" prefWidth="110.0" />
               <DatePicker fx:id="dateFinFilter" prefWidth="110.0" />
               
               <Button onAction="#handleNouvelAccident" style="-fx-background-color: #e74c3c; -fx-text-fill: white;" text="Nouveau" />
            </HBox>
            
            <!-- Tableau des accidents -->
            <TableView fx:id="accidentTable" VBox.vgrow="ALWAYS">
               <columns>
                  <TableColumn fx:id="idColumn" prefWidth="40.0" text="ID" />
                  <TableColumn fx:id="vehiculeColumn" prefWidth="120.0" text="Véhicule" />
                  <TableColumn fx:id="clientColumn" prefWidth="120.0" text="Client" />
                  <TableColumn fx:id="dateColumn" prefWidth="80.0" text="Date" />
                  <TableColumn fx:id="lieuColumn" prefWidth="150.0" text="Lieu" />
                  <TableColumn fx:id="typeColumn" prefWidth="100.0" text="Type" />
                  <TableColumn fx:id="graviteColumn" prefWidth="80.0" text="Gravité" />
                  <TableColumn fx:id="statutColumn" prefWidth="90.0" text="Statut" />
                  <TableColumn fx:id="degatsColumn" prefWidth="80.0" text="Dégâts" />
               </columns>
            </TableView>
            
            <!-- Boutons d'action -->
            <HBox alignment="CENTER" spacing="10.0">
               <Button onAction="#handleModifierAccident" style="-fx-background-color: #3498db; -fx-text-fill: white;" text="Modifier" />
               <Button onAction="#handleVoirDetails" style="-fx-background-color: #9b59b6; -fx-text-fill: white;" text="Détails" />
               <Button onAction="#handleDeclareAssurance" style="-fx-background-color: #f39c12; -fx-text-fill: white;" text="Assurance" />
            </HBox>
         </VBox>
         
         <!-- Formulaire d'accident -->
         <ScrollPane fitToWidth="true" style="-fx-background-color: #ecf0f1;">
            <VBox spacing="15.0">
               <padding>
                  <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
               </padding>
               
               <Label text="Déclaration d'Accident" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #c0392b;" />
               
               <!-- Onglets pour organiser le formulaire -->
               <TabPane>
                  <!-- Onglet Informations générales -->
                  <Tab text="Général" closable="false">
                     <GridPane hgap="10.0" vgap="15.0">
                        <columnConstraints>
                           <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                           <ColumnConstraints hgrow="ALWAYS" />
                        </columnConstraints>
                        
                        <Label text="Véhicule:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <ComboBox fx:id="vehiculeComboBox" maxWidth="1.7976931348623157E308" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <Label text="Location:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <ComboBox fx:id="locationComboBox" maxWidth="1.7976931348623157E308" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <Label text="Date accident:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <DatePicker fx:id="dateAccidentPicker" maxWidth="1.7976931348623157E308" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                        
                        <Label text="Heure:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                        <TextField fx:id="heureField" maxWidth="1.7976931348623157E308" promptText="HH:MM" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                        
                        <Label text="Lieu:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                        <TextField fx:id="lieuField" maxWidth="1.7976931348623157E308" promptText="Adresse précise..." GridPane.columnIndex="1" GridPane.rowIndex="4" />
                        
                        <Label text="Ville:" GridPane.columnIndex="0" GridPane.rowIndex="5" />
                        <TextField fx:id="villeField" maxWidth="1.7976931348623157E308" promptText="Ville..." GridPane.columnIndex="1" GridPane.rowIndex="5" />
                        
                        <Label text="Type accident:" GridPane.columnIndex="0" GridPane.rowIndex="6" />
                        <ComboBox fx:id="typeAccidentComboBox" maxWidth="1.7976931348623157E308" GridPane.columnIndex="1" GridPane.rowIndex="6" />
                        
                        <Label text="Gravité:" GridPane.columnIndex="0" GridPane.rowIndex="7" />
                        <ComboBox fx:id="graviteComboBox" maxWidth="1.7976931348623157E308" GridPane.columnIndex="1" GridPane.rowIndex="7" />
                        
                        <Label text="Description:" GridPane.columnIndex="0" GridPane.rowIndex="8" />
                        <TextArea fx:id="descriptionArea" maxWidth="1.7976931348623157E308" prefRowCount="3" promptText="Description détaillée de l'accident..." GridPane.columnIndex="1" GridPane.rowIndex="8" />
                     </GridPane>
                  </Tab>
                  
                  <!-- Onglet Circonstances -->
                  <Tab text="Circonstances" closable="false">
                     <GridPane hgap="10.0" vgap="15.0">
                        <columnConstraints>
                           <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                           <ColumnConstraints hgrow="ALWAYS" />
                        </columnConstraints>
                        
                        <Label text="Circonstances:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <TextArea fx:id="circonstancesArea" maxWidth="1.7976931348623157E308" prefRowCount="3" promptText="Circonstances de l'accident..." GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <Label text="Météo:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <TextField fx:id="meteoField" maxWidth="1.7976931348623157E308" promptText="Conditions météorologiques..." GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <Label text="État chaussée:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <TextField fx:id="chausseeField" maxWidth="1.7976931348623157E308" promptText="État de la chaussée..." GridPane.columnIndex="1" GridPane.rowIndex="2" />
                        
                        <Label text="Visibilité:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                        <TextField fx:id="visibiliteField" maxWidth="1.7976931348623157E308" promptText="Conditions de visibilité..." GridPane.columnIndex="1" GridPane.rowIndex="3" />
                        
                        <Label text="Responsabilité:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                        <ComboBox fx:id="responsabiliteComboBox" maxWidth="1.7976931348623157E308" GridPane.columnIndex="1" GridPane.rowIndex="4" />
                        
                        <Label text="Détails responsabilité:" GridPane.columnIndex="0" GridPane.rowIndex="5" />
                        <TextArea fx:id="detailsResponsabiliteArea" maxWidth="1.7976931348623157E308" prefRowCount="2" promptText="Détails sur la responsabilité..." GridPane.columnIndex="1" GridPane.rowIndex="5" />
                     </GridPane>
                  </Tab>
                  
                  <!-- Onglet Dégâts et blessés -->
                  <Tab text="Dégâts" closable="false">
                     <GridPane hgap="10.0" vgap="15.0">
                        <columnConstraints>
                           <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                           <ColumnConstraints hgrow="ALWAYS" />
                        </columnConstraints>
                        
                        <Label text="Blessés client:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <CheckBox fx:id="blessesClientCheck" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <Label text="Blessés tiers:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <CheckBox fx:id="blessesTiersCheck" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <Label text="Blessés légers:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <TextField fx:id="blessesLegersField" maxWidth="1.7976931348623157E308" promptText="Nombre..." GridPane.columnIndex="1" GridPane.rowIndex="2" />
                        
                        <Label text="Blessés graves:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                        <TextField fx:id="blessesGravesField" maxWidth="1.7976931348623157E308" promptText="Nombre..." GridPane.columnIndex="1" GridPane.rowIndex="3" />
                        
                        <Label text="Décès:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                        <TextField fx:id="decesField" maxWidth="1.7976931348623157E308" promptText="Nombre..." GridPane.columnIndex="1" GridPane.rowIndex="4" />
                        
                        <Label text="Dégâts véhicule:" GridPane.columnIndex="0" GridPane.rowIndex="5" />
                        <TextArea fx:id="degatsVehiculeArea" maxWidth="1.7976931348623157E308" prefRowCount="2" promptText="Description des dégâts..." GridPane.columnIndex="1" GridPane.rowIndex="5" />
                        
                        <Label text="Estimation dégâts:" GridPane.columnIndex="0" GridPane.rowIndex="6" />
                        <TextField fx:id="estimationField" maxWidth="1.7976931348623157E308" promptText="Montant en DH..." GridPane.columnIndex="1" GridPane.rowIndex="6" />
                        
                        <Label text="Autres véhicules:" GridPane.columnIndex="0" GridPane.rowIndex="7" />
                        <CheckBox fx:id="autresVehiculesCheck" GridPane.columnIndex="1" GridPane.rowIndex="7" />
                        
                        <Label text="Nombre véhicules:" GridPane.columnIndex="0" GridPane.rowIndex="8" />
                        <TextField fx:id="nombreVehiculesField" maxWidth="1.7976931348623157E308" promptText="Nombre..." GridPane.columnIndex="1" GridPane.rowIndex="8" />
                     </GridPane>
                  </Tab>
                  
                  <!-- Onglet Assurance -->
                  <Tab text="Assurance" closable="false">
                     <GridPane hgap="10.0" vgap="15.0">
                        <columnConstraints>
                           <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                           <ColumnConstraints hgrow="ALWAYS" />
                        </columnConstraints>
                        
                        <Label text="N° constat amiable:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <TextField fx:id="constatField" maxWidth="1.7976931348623157E308" promptText="Numéro du constat..." GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <Label text="N° déclaration police:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <TextField fx:id="declarationPoliceField" maxWidth="1.7976931348623157E308" promptText="Numéro PV..." GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <Label text="Commissariat:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <TextField fx:id="commissariatField" maxWidth="1.7976931348623157E308" promptText="Commissariat compétent..." GridPane.columnIndex="1" GridPane.rowIndex="2" />
                        
                        <Label text="Compagnie assurance:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                        <TextField fx:id="compagnieField" maxWidth="1.7976931348623157E308" promptText="Nom de la compagnie..." GridPane.columnIndex="1" GridPane.rowIndex="3" />
                        
                        <Label text="N° police:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                        <TextField fx:id="policeField" maxWidth="1.7976931348623157E308" promptText="Numéro de police..." GridPane.columnIndex="1" GridPane.rowIndex="4" />
                        
                        <Label text="N° sinistre:" GridPane.columnIndex="0" GridPane.rowIndex="5" />
                        <TextField fx:id="sinistreField" maxWidth="1.7976931348623157E308" promptText="Numéro de sinistre..." GridPane.columnIndex="1" GridPane.rowIndex="5" />
                        
                        <Label text="Montant indemnisation:" GridPane.columnIndex="0" GridPane.rowIndex="6" />
                        <TextField fx:id="indemnisationField" maxWidth="1.7976931348623157E308" promptText="Montant en DH..." GridPane.columnIndex="1" GridPane.rowIndex="6" />
                        
                        <Label text="Prise en charge:" GridPane.columnIndex="0" GridPane.rowIndex="7" />
                        <CheckBox fx:id="priseEnChargeCheck" GridPane.columnIndex="1" GridPane.rowIndex="7" />
                        
                        <Label text="Véhicule réparable:" GridPane.columnIndex="0" GridPane.rowIndex="8" />
                        <CheckBox fx:id="reparableCheck" selected="true" GridPane.columnIndex="1" GridPane.rowIndex="8" />
                     </GridPane>
                  </Tab>
               </TabPane>
               
               <!-- Boutons de sauvegarde -->
               <HBox alignment="CENTER" spacing="10.0">
                  <Button onAction="#handleSauvegarderAccident" style="-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-font-size: 14px;" text="Sauvegarder" />
                  <Button onAction="#handleNouvelAccident" style="-fx-background-color: #95a5a6; -fx-text-fill: white;" text="Nouveau" />
               </HBox>
            </VBox>
         </ScrollPane>
      </SplitPane>
   </center>
</BorderPane>
