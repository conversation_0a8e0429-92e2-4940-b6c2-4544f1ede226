-- Script SQL pour créer les nouvelles tables du système de location
-- Conforme à la réglementation marocaine des entreprises de location de voitures

USE locationdb;

-- Table Prolongation - Gestion des extensions de location
CREATE TABLE IF NOT EXISTS prolongation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    location_id BIGINT NOT NULL,
    ancienneDateFin DATE,
    nouvelleDateFin DATE,
    dateDemandeProlong DATE,
    dateApprouvee DATE,
    prixProlong DOUBLE DEFAULT 0,
    cautionSupplementaire DOUBLE DEFAULT 0,
    penaliteRetard DOUBLE DEFAULT 0,
    statut ENUM('DEMANDEE', 'APPROUVEE', 'REFUSEE', 'ANNULEE', 'TERMINEE') DEFAULT 'DEMANDEE',
    motifProlong TEXT,
    notesAgent TEXT,
    agentApprouveur VARCHAR(100),
    verificationPermisValide BOOLEAN DEFAULT FALSE,
    verificationAssuranceValide BOOLEAN DEFAULT FALSE,
    controleVehicule BOOLEAN DEFAULT FALSE,
    kilometrageActuel INT,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (location_id) REFERENCES location(id) ON DELETE CASCADE,
    INDEX idx_prolongation_location (location_id),
    INDEX idx_prolongation_statut (statut),
    INDEX idx_prolongation_date_demande (dateDemandeProlong)
);

-- Table HistoriqueVehicule - Suivi complet de l'historique des véhicules
CREATE TABLE IF NOT EXISTS historique_vehicule (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    vehicule_id BIGINT NOT NULL,
    client_id BIGINT,
    location_id BIGINT,
    typeEvenement ENUM(
        'ACQUISITION', 'LOCATION_DEBUT', 'LOCATION_FIN', 'MAINTENANCE', 
        'ACCIDENT', 'CONTROLE_TECHNIQUE', 'REPARATION', 'CHANGEMENT_ETAT',
        'MISE_HORS_SERVICE', 'VENTE', 'VOL', 'SINISTRE'
    ) NOT NULL,
    dateEvenement TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    description TEXT NOT NULL,
    detailsEvenement TEXT,
    kilometrageAvant INT,
    kilometrageApres INT,
    kilometrageParcouru INT,
    coutEvenement DOUBLE,
    numeroFacture VARCHAR(50),
    fournisseur VARCHAR(200),
    agentResponsable VARCHAR(100),
    numeroRapport VARCHAR(50),
    documentsJoints TEXT,
    numeroPoliceAssurance VARCHAR(50),
    etatVehiculeApres VARCHAR(50),
    observationsAgent TEXT,
    numeroDeclarationPolice VARCHAR(50),
    numeroConstatAmiable VARCHAR(50),
    declarationAssurance BOOLEAN DEFAULT FALSE,
    dateDeclarationAssurance DATE,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    createdBy VARCHAR(100),
    FOREIGN KEY (vehicule_id) REFERENCES vehicule(id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES client(id) ON DELETE SET NULL,
    FOREIGN KEY (location_id) REFERENCES location(id) ON DELETE SET NULL,
    INDEX idx_historique_vehicule (vehicule_id),
    INDEX idx_historique_type (typeEvenement),
    INDEX idx_historique_date (dateEvenement),
    INDEX idx_historique_client (client_id)
);

-- Table Maintenance - Gestion de la maintenance des véhicules
CREATE TABLE IF NOT EXISTS maintenance (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    vehicule_id BIGINT NOT NULL,
    typeMaintenance ENUM(
        'PREVENTIVE', 'CORRECTIVE', 'CONTROLE_TECHNIQUE', 'VIDANGE',
        'REVISION', 'REPARATION', 'CHANGEMENT_PNEUS', 'FREINAGE',
        'CLIMATISATION', 'CARROSSERIE', 'ELECTRICITE', 'URGENCE'
    ) NOT NULL,
    statut ENUM('PROGRAMMEE', 'EN_COURS', 'TERMINEE', 'ANNULEE', 'REPORTEE', 'EN_ATTENTE_PIECES') DEFAULT 'PROGRAMMEE',
    dateProgrammee DATE,
    dateDebut DATE,
    dateFin DATE,
    prochaineMaintenance DATE,
    kilometrageActuel INT,
    prochainKilometrageMaintenance INT,
    intervalleKilometrage INT,
    description TEXT,
    travauEffectues TEXT,
    piecesChangees TEXT,
    observationsAvant TEXT,
    observationsApres TEXT,
    coutMainOeuvre DOUBLE DEFAULT 0,
    coutPieces DOUBLE DEFAULT 0,
    coutTotal DOUBLE DEFAULT 0,
    numeroFacture VARCHAR(50),
    facturePayee BOOLEAN DEFAULT FALSE,
    nomGarage VARCHAR(200),
    adresseGarage TEXT,
    telephoneGarage VARCHAR(20),
    technicienResponsable VARCHAR(100),
    numeroOrdreReparation VARCHAR(50),
    sousGarantie BOOLEAN DEFAULT FALSE,
    finGarantie DATE,
    numeroGarantie VARCHAR(50),
    conformiteReglementaire BOOLEAN DEFAULT FALSE,
    documentsJoints TEXT,
    certificatConformite VARCHAR(255),
    rapportTechnique TEXT,
    priorite ENUM('FAIBLE', 'NORMALE', 'ELEVEE', 'CRITIQUE', 'URGENTE') DEFAULT 'NORMALE',
    vehiculeImmobilise BOOLEAN DEFAULT FALSE,
    dureeEstimeeJours INT,
    dureeReelleJours INT,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    createdBy VARCHAR(100),
    updatedBy VARCHAR(100),
    FOREIGN KEY (vehicule_id) REFERENCES vehicule(id) ON DELETE CASCADE,
    INDEX idx_maintenance_vehicule (vehicule_id),
    INDEX idx_maintenance_statut (statut),
    INDEX idx_maintenance_type (typeMaintenance),
    INDEX idx_maintenance_date_programmee (dateProgrammee),
    INDEX idx_maintenance_priorite (priorite)
);

-- Table Accident - Gestion des accidents de véhicules
CREATE TABLE IF NOT EXISTS accident (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    vehicule_id BIGINT NOT NULL,
    location_id BIGINT,
    client_id BIGINT,
    dateAccident DATE NOT NULL,
    heureAccident TIME,
    lieuAccident TEXT,
    villeAccident VARCHAR(100),
    coordonneesGPS VARCHAR(100),
    typeAccident ENUM(
        'COLLISION_FRONTALE', 'COLLISION_ARRIERE', 'COLLISION_LATERALE',
        'SORTIE_ROUTE', 'RENVERSEMENT', 'CHOC_OBSTACLE', 'INCENDIE',
        'VOL', 'VANDALISME', 'CATASTROPHE_NATURELLE', 'AUTRE'
    ) NOT NULL,
    gravite ENUM('LEGER', 'MOYEN', 'GRAVE', 'TRES_GRAVE', 'MORTEL') NOT NULL,
    descriptionAccident TEXT,
    circonstancesAccident TEXT,
    conditionsMeteo VARCHAR(100),
    etatChaussee VARCHAR(100),
    visibilite VARCHAR(100),
    responsabilite ENUM(
        'CLIENT_RESPONSABLE', 'TIERS_RESPONSABLE', 'RESPONSABILITE_PARTAGEE',
        'FORCE_MAJEURE', 'EN_COURS_DETERMINATION'
    ),
    detailsResponsabilite TEXT,
    blessesClient BOOLEAN DEFAULT FALSE,
    blessesTiers BOOLEAN DEFAULT FALSE,
    nombreBlessesLegers INT DEFAULT 0,
    nombreBlessesGraves INT DEFAULT 0,
    nombreDeces INT DEFAULT 0,
    autresVehiculesImpliques BOOLEAN DEFAULT FALSE,
    nombreVehiculesImpliques INT DEFAULT 0,
    detailsAutresVehicules TEXT,
    degatsVehiculeLoue TEXT,
    degatsAutresVehicules TEXT,
    degatsInfrastructure TEXT,
    estimationDegats DOUBLE,
    numeroConstatAmiable VARCHAR(50),
    numeroDeclarationPolice VARCHAR(50),
    commissariatCompetent VARCHAR(200),
    numeroProcessVerbal VARCHAR(50),
    alcoolemieEffectuee BOOLEAN DEFAULT FALSE,
    resultatAlcoolemie VARCHAR(100),
    compagnieAssurance VARCHAR(200),
    numeroPoliceAssurance VARCHAR(50),
    numeroDeclarationAssurance VARCHAR(50),
    dateDeclarationAssurance DATE,
    numeroSinistre VARCHAR(50),
    priseEnChargeAssurance BOOLEAN DEFAULT FALSE,
    temoinsPresents BOOLEAN DEFAULT FALSE,
    detailsTemoin1 TEXT,
    detailsTemoin2 TEXT,
    detailsTemoin3 TEXT,
    photosAccident TEXT,
    rapportPolice VARCHAR(255),
    rapportExpertise VARCHAR(255),
    certificatsMedicaux TEXT,
    autresDocuments TEXT,
    statut ENUM('DECLARE', 'EN_COURS_ENQUETE', 'EXPERTISE_EN_COURS', 'RESOLU', 'CONTENTIEUX', 'CLOS') DEFAULT 'DECLARE',
    montantIndemnisation DOUBLE,
    montantFranchise DOUBLE,
    montantRecupere DOUBLE,
    vehiculeReparable BOOLEAN DEFAULT TRUE,
    vehiculeEconomiquementIrreparable BOOLEAN DEFAULT FALSE,
    dateRemiseEnService DATE,
    coutReparation DOUBLE,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    agentDeclarant VARCHAR(100),
    derniereModificationPar VARCHAR(100),
    FOREIGN KEY (vehicule_id) REFERENCES vehicule(id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES location(id) ON DELETE SET NULL,
    FOREIGN KEY (client_id) REFERENCES client(id) ON DELETE SET NULL,
    INDEX idx_accident_vehicule (vehicule_id),
    INDEX idx_accident_date (dateAccident),
    INDEX idx_accident_type (typeAccident),
    INDEX idx_accident_gravite (gravite),
    INDEX idx_accident_statut (statut),
    INDEX idx_accident_client (client_id)
);

-- Mise à jour de la table Location pour les nouvelles fonctionnalités
ALTER TABLE location 
ADD COLUMN IF NOT EXISTS contractNumber VARCHAR(50),
ADD COLUMN IF NOT EXISTS pickupLocation VARCHAR(200),
ADD COLUMN IF NOT EXISTS deliveryLocation VARCHAR(200),
ADD COLUMN IF NOT EXISTS insuranceType VARCHAR(50),
ADD COLUMN IF NOT EXISTS fuelPolicy VARCHAR(50),
ADD COLUMN IF NOT EXISTS caution DOUBLE DEFAULT 0,
ADD COLUMN IF NOT EXISTS notes TEXT,
ADD COLUMN IF NOT EXISTS createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS optionGPS BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS optionSiegeEnfant BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS optionChauffeur BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS optionAssuranceComplete BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS optionKilometrageIllimite BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS prixOptions DOUBLE DEFAULT 0,
ADD COLUMN IF NOT EXISTS kilometrageDebut INT,
ADD COLUMN IF NOT EXISTS kilometrageFin INT,
ADD COLUMN IF NOT EXISTS kilometrageAutorise INT,
ADD COLUMN IF NOT EXISTS penaliteKilometrage DOUBLE DEFAULT 0,
ADD COLUMN IF NOT EXISTS status ENUM('RESERVE', 'EN_COURS', 'TERMINE', 'ANNULE', 'PROLONGE') DEFAULT 'RESERVE';

-- Mise à jour de la table Paiement pour les nouvelles fonctionnalités
ALTER TABLE paiement
ADD COLUMN IF NOT EXISTS heureTransaction TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS typePaiement ENUM('ACOMPTE', 'SOLDE', 'CAUTION', 'PENALITE', 'SUPPLEMENT', 'REMBOURSEMENT', 'PROLONGATION', 'REPARATION') DEFAULT 'SOLDE',
ADD COLUMN IF NOT EXISTS methodePaiement ENUM('ESPECES', 'CARTE_BANCAIRE', 'CHEQUE', 'VIREMENT', 'MOBILE_MONEY', 'CARTE_CREDIT', 'PAYPAL', 'AUTRE') DEFAULT 'ESPECES',
ADD COLUMN IF NOT EXISTS statut ENUM('EN_ATTENTE', 'VALIDE', 'REFUSE', 'ANNULE', 'REMBOURSE', 'EN_COURS', 'EXPIRE') DEFAULT 'EN_ATTENTE',
ADD COLUMN IF NOT EXISTS numeroTransaction VARCHAR(100),
ADD COLUMN IF NOT EXISTS numeroRecu VARCHAR(50),
ADD COLUMN IF NOT EXISTS referenceInterne VARCHAR(50),
ADD COLUMN IF NOT EXISTS numeroAutorisation VARCHAR(50),
ADD COLUMN IF NOT EXISTS banqueEmettrice VARCHAR(100),
ADD COLUMN IF NOT EXISTS numeroCompte VARCHAR(50),
ADD COLUMN IF NOT EXISTS nomPorteurCarte VARCHAR(100),
ADD COLUMN IF NOT EXISTS dernierQuatreChiffres VARCHAR(4),
ADD COLUMN IF NOT EXISTS montantHT DOUBLE DEFAULT 0,
ADD COLUMN IF NOT EXISTS montantTVA DOUBLE DEFAULT 0,
ADD COLUMN IF NOT EXISTS tauxTVA DOUBLE DEFAULT 20.0,
ADD COLUMN IF NOT EXISTS numeroFacture VARCHAR(50),
ADD COLUMN IF NOT EXISTS factureEmise BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS deviseOriginale VARCHAR(3),
ADD COLUMN IF NOT EXISTS montantDeviseOriginale DOUBLE,
ADD COLUMN IF NOT EXISTS tauxChange DOUBLE,
ADD COLUMN IF NOT EXISTS fraisBancaires DOUBLE DEFAULT 0,
ADD COLUMN IF NOT EXISTS commission DOUBLE DEFAULT 0,
ADD COLUMN IF NOT EXISTS montantNet DOUBLE DEFAULT 0,
ADD COLUMN IF NOT EXISTS remboursable BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS dateRemboursement DATE,
ADD COLUMN IF NOT EXISTS montantRembourse DOUBLE DEFAULT 0,
ADD COLUMN IF NOT EXISTS motifRemboursement TEXT,
ADD COLUMN IF NOT EXISTS createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS agentEncaisseur VARCHAR(100),
ADD COLUMN IF NOT EXISTS agentValidateur VARCHAR(100),
ADD COLUMN IF NOT EXISTS commentaires TEXT;

-- Ajout d'index pour optimiser les performances
CREATE INDEX IF NOT EXISTS idx_location_contract ON location(contractNumber);
CREATE INDEX IF NOT EXISTS idx_location_status ON location(status);
CREATE INDEX IF NOT EXISTS idx_location_created ON location(createdAt);

CREATE INDEX IF NOT EXISTS idx_paiement_type ON paiement(typePaiement);
CREATE INDEX IF NOT EXISTS idx_paiement_methode ON paiement(methodePaiement);
CREATE INDEX IF NOT EXISTS idx_paiement_statut ON paiement(statut);
CREATE INDEX IF NOT EXISTS idx_paiement_transaction ON paiement(numeroTransaction);

-- Vues pour faciliter les requêtes complexes
CREATE OR REPLACE VIEW vue_locations_completes AS
SELECT 
    l.*,
    c.nom as client_nom,
    c.prenom as client_prenom,
    c.cin as client_cin,
    c.telephone as client_telephone,
    v.marque as vehicule_marque,
    v.modele as vehicule_modele,
    v.immatriculation as vehicule_immatriculation,
    v.prixParJour as vehicule_prix_jour,
    COALESCE(p.total_paiements, 0) as total_paye
FROM location l
LEFT JOIN client c ON l.client_id = c.id
LEFT JOIN vehicule v ON l.vehicule_id = v.id
LEFT JOIN (
    SELECT location_id, SUM(montant) as total_paiements
    FROM paiement 
    WHERE statut = 'VALIDE'
    GROUP BY location_id
) p ON l.id = p.location_id;

CREATE OR REPLACE VIEW vue_vehicules_maintenance AS
SELECT 
    v.*,
    COUNT(m.id) as nombre_maintenances,
    MAX(m.dateFin) as derniere_maintenance,
    SUM(CASE WHEN m.statut = 'PROGRAMMEE' THEN 1 ELSE 0 END) as maintenances_programmees,
    SUM(CASE WHEN m.statut = 'EN_COURS' THEN 1 ELSE 0 END) as maintenances_en_cours,
    COALESCE(SUM(m.coutTotal), 0) as cout_total_maintenance
FROM vehicule v
LEFT JOIN maintenance m ON v.id = m.vehicule_id
GROUP BY v.id;

CREATE OR REPLACE VIEW vue_statistiques_accidents AS
SELECT 
    v.id as vehicule_id,
    v.immatriculation,
    v.marque,
    v.modele,
    COUNT(a.id) as nombre_accidents,
    SUM(CASE WHEN a.gravite IN ('GRAVE', 'TRES_GRAVE', 'MORTEL') THEN 1 ELSE 0 END) as accidents_graves,
    SUM(CASE WHEN a.responsabilite = 'CLIENT_RESPONSABLE' THEN 1 ELSE 0 END) as accidents_client_responsable,
    COALESCE(SUM(a.estimationDegats), 0) as cout_total_degats
FROM vehicule v
LEFT JOIN accident a ON v.id = a.vehicule_id
GROUP BY v.id;

-- Triggers pour maintenir la cohérence des données
DELIMITER //

CREATE TRIGGER IF NOT EXISTS tr_location_update_vehicule_status
AFTER UPDATE ON location
FOR EACH ROW
BEGIN
    IF NEW.status = 'EN_COURS' AND OLD.status != 'EN_COURS' THEN
        UPDATE vehicule SET etat = 'loué' WHERE id = NEW.vehicule_id;
    ELSEIF NEW.status = 'TERMINE' AND OLD.status != 'TERMINE' THEN
        UPDATE vehicule SET etat = 'disponible', lastUsed = NEW.dateFinReelle WHERE id = NEW.vehicule_id;
    END IF;
END//

CREATE TRIGGER IF NOT EXISTS tr_maintenance_update_vehicule_status
AFTER UPDATE ON maintenance
FOR EACH ROW
BEGIN
    IF NEW.statut = 'EN_COURS' AND NEW.vehiculeImmobilise = TRUE THEN
        UPDATE vehicule SET etat = 'en panne' WHERE id = NEW.vehicule_id;
    ELSEIF NEW.statut = 'TERMINEE' THEN
        UPDATE vehicule SET etat = 'disponible' WHERE id = NEW.vehicule_id;
    END IF;
END//

CREATE TRIGGER IF NOT EXISTS tr_accident_update_vehicule_status
AFTER INSERT ON accident
FOR EACH ROW
BEGIN
    IF NEW.gravite IN ('GRAVE', 'TRES_GRAVE', 'MORTEL') OR NEW.vehiculeEconomiquementIrreparable = TRUE THEN
        UPDATE vehicule SET etat = 'en panne' WHERE id = NEW.vehicule_id;
    END IF;
END//

DELIMITER ;

-- Insertion de données de test pour les nouvelles fonctionnalités
-- (Ces données seront ajoutées via le DatabaseInitializer Java)

COMMIT;
