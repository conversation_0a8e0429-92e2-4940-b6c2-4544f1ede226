package controller;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.stage.Stage;
import javafx.event.ActionEvent;
import javafx.scene.layout.GridPane;
import javafx.geometry.Insets;
import javafx.scene.control.Alert.AlertType;
import javafx.application.Platform;
import javafx.util.StringConverter;

import dao.ProlongationDAO;
import dao.LocationDAO;
import dao.VehiculeDAO;
import model.Prolongation;
import model.Location;
import model.Vehicule;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

/**
 * Contrôleur pour la gestion des prolongations de location
 * Conforme à la réglementation marocaine
 */
public class ProlongationController {

    @FXML private TableView<Prolongation> prolongationTable;
    @FXML private TableColumn<Prolongation, Long> idColumn;
    @FXML private TableColumn<Prolongation, String> locationColumn;
    @FXML private TableColumn<Prolongation, String> clientColumn;
    @FXML private TableColumn<Prolongation, String> vehiculeColumn;
    @FXML private TableColumn<Prolongation, String> ancienneDateColumn;
    @FXML private TableColumn<Prolongation, String> nouvelleDateColumn;
    @FXML private TableColumn<Prolongation, String> dateDemandeeColumn;
    @FXML private TableColumn<Prolongation, String> statutColumn;
    @FXML private TableColumn<Prolongation, Double> prixColumn;
    @FXML private TableColumn<Prolongation, String> agentColumn;

    @FXML private TextField searchField;
    @FXML private ComboBox<Prolongation.StatutProlong> statutFilter;
    @FXML private DatePicker dateDebutFilter;
    @FXML private DatePicker dateFinFilter;
    @FXML private Label lblTotalCount;
    @FXML private Label lblEnAttente;
    @FXML private Label lblApprouvees;
    @FXML private Label lblRefusees;

    // Formulaire de prolongation
    @FXML private ComboBox<Location> locationComboBox;
    @FXML private DatePicker nouvelleDateFinPicker;
    @FXML private TextField motifField;
    @FXML private TextArea notesArea;
    @FXML private TextField prixProlongField;
    @FXML private CheckBox verificationPermisCheck;
    @FXML private CheckBox verificationAssuranceCheck;
    @FXML private CheckBox controleVehiculeCheck;
    @FXML private TextField kilometrageField;

    private ProlongationDAO prolongationDAO;
    private LocationDAO locationDAO;
    private VehiculeDAO vehiculeDAO;
    private ObservableList<Prolongation> prolongationList;
    private ObservableList<Location> locationList;

    private boolean isEditMode = false;
    private Prolongation selectedProlongation = null;

    @FXML
    public void initialize() {
        prolongationDAO = new ProlongationDAO();
        locationDAO = new LocationDAO();
        vehiculeDAO = new VehiculeDAO();

        setupTableColumns();
        setupFilters();
        setupFormControls();
        loadData();
        updateStatistics();
    }

    private void setupTableColumns() {
        idColumn.setCellValueFactory(new PropertyValueFactory<>("id"));
        
        locationColumn.setCellValueFactory(data -> {
            Location loc = data.getValue().getLocation();
            return new javafx.beans.property.SimpleStringProperty(
                loc != null ? "LOC-" + loc.getId() : "");
        });
        
        clientColumn.setCellValueFactory(data -> {
            Location loc = data.getValue().getLocation();
            if (loc != null && loc.getClient() != null) {
                return new javafx.beans.property.SimpleStringProperty(
                    loc.getClient().getNom() + " " + loc.getClient().getPrenom());
            }
            return new javafx.beans.property.SimpleStringProperty("");
        });
        
        vehiculeColumn.setCellValueFactory(data -> {
            Location loc = data.getValue().getLocation();
            if (loc != null && loc.getVehicule() != null) {
                Vehicule v = loc.getVehicule();
                return new javafx.beans.property.SimpleStringProperty(
                    v.getMarque() + " " + v.getModele() + " (" + v.getImmatriculation() + ")");
            }
            return new javafx.beans.property.SimpleStringProperty("");
        });
        
        ancienneDateColumn.setCellValueFactory(data -> {
            LocalDate date = data.getValue().getAncienneDateFin();
            return new javafx.beans.property.SimpleStringProperty(
                date != null ? date.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "");
        });
        
        nouvelleDateColumn.setCellValueFactory(data -> {
            LocalDate date = data.getValue().getNouvelleDateFin();
            return new javafx.beans.property.SimpleStringProperty(
                date != null ? date.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "");
        });
        
        dateDemandeeColumn.setCellValueFactory(data -> {
            LocalDate date = data.getValue().getDateDemandeProlong();
            return new javafx.beans.property.SimpleStringProperty(
                date != null ? date.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "");
        });
        
        statutColumn.setCellValueFactory(data -> {
            Prolongation.StatutProlong statut = data.getValue().getStatut();
            return new javafx.beans.property.SimpleStringProperty(
                statut != null ? getStatutDisplayName(statut) : "");
        });
        
        prixColumn.setCellValueFactory(new PropertyValueFactory<>("prixProlong"));
        prixColumn.setCellFactory(col -> new TableCell<Prolongation, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(String.format("%.2f DH", item));
                }
            }
        });
        
        agentColumn.setCellValueFactory(new PropertyValueFactory<>("agentApprouveur"));

        // Style des cellules selon le statut
        prolongationTable.setRowFactory(tv -> {
            TableRow<Prolongation> row = new TableRow<>();
            row.itemProperty().addListener((obs, oldItem, newItem) -> {
                if (newItem == null) {
                    row.setStyle("");
                } else {
                    switch (newItem.getStatut()) {
                        case DEMANDEE:
                            row.setStyle("-fx-background-color: #fff3cd;");
                            break;
                        case APPROUVEE:
                            row.setStyle("-fx-background-color: #d4edda;");
                            break;
                        case REFUSEE:
                            row.setStyle("-fx-background-color: #f8d7da;");
                            break;
                        case ANNULEE:
                            row.setStyle("-fx-background-color: #e2e3e5;");
                            break;
                        default:
                            row.setStyle("");
                    }
                }
            });
            return row;
        });
    }

    private void setupFilters() {
        // Filtre par statut
        statutFilter.setItems(FXCollections.observableArrayList(Prolongation.StatutProlong.values()));
        statutFilter.setConverter(new StringConverter<Prolongation.StatutProlong>() {
            @Override
            public String toString(Prolongation.StatutProlong statut) {
                return statut != null ? getStatutDisplayName(statut) : "";
            }

            @Override
            public Prolongation.StatutProlong fromString(String string) {
                return null;
            }
        });

        // Listeners pour les filtres
        searchField.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        statutFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        dateDebutFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        dateFinFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
    }

    private void setupFormControls() {
        // Configuration du ComboBox des locations
        locationList = FXCollections.observableArrayList();
        locationComboBox.setItems(locationList);
        locationComboBox.setConverter(new StringConverter<Location>() {
            @Override
            public String toString(Location location) {
                if (location == null) return "";
                String client = location.getClient() != null ? 
                    location.getClient().getNom() + " " + location.getClient().getPrenom() : "Client inconnu";
                String vehicule = location.getVehicule() != null ? 
                    location.getVehicule().getImmatriculation() : "Véhicule inconnu";
                return String.format("LOC-%d - %s - %s", location.getId(), client, vehicule);
            }

            @Override
            public Location fromString(String string) {
                return null;
            }
        });

        // Validation des champs numériques
        prixProlongField.textProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal.matches("\\d*(\\.\\d*)?")) {
                prixProlongField.setText(oldVal);
            }
        });

        kilometrageField.textProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal.matches("\\d*")) {
                kilometrageField.setText(oldVal);
            }
        });

        // Calcul automatique du prix
        locationComboBox.valueProperty().addListener((obs, oldVal, newVal) -> calculerPrixProlong());
        nouvelleDateFinPicker.valueProperty().addListener((obs, oldVal, newVal) -> calculerPrixProlong());
    }

    private void loadData() {
        try {
            // Charger les prolongations
            List<Prolongation> prolongations = prolongationDAO.findAll();
            prolongationList = FXCollections.observableArrayList(prolongations);
            prolongationTable.setItems(prolongationList);

            // Charger les locations actives pour le formulaire
            List<Location> locations = locationDAO.findAll().stream()
                .filter(loc -> loc.getStatus() == Location.Status.EN_COURS || 
                              loc.getStatus() == Location.Status.RESERVE)
                .toList();
            locationList.setAll(locations);

        } catch (Exception e) {
            showAlert("Erreur lors du chargement des données: " + e.getMessage(), AlertType.ERROR);
            e.printStackTrace();
        }
    }

    private void applyFilters() {
        if (prolongationList == null) return;

        ObservableList<Prolongation> filteredList = FXCollections.observableArrayList();
        String searchText = searchField.getText().toLowerCase();
        Prolongation.StatutProlong selectedStatut = statutFilter.getValue();
        LocalDate dateDebut = dateDebutFilter.getValue();
        LocalDate dateFin = dateFinFilter.getValue();

        for (Prolongation prolongation : prolongationList) {
            boolean matches = true;

            // Filtre par texte de recherche
            if (!searchText.isEmpty()) {
                String searchableText = "";
                if (prolongation.getLocation() != null) {
                    if (prolongation.getLocation().getClient() != null) {
                        searchableText += prolongation.getLocation().getClient().getNom() + " " +
                                        prolongation.getLocation().getClient().getPrenom() + " ";
                    }
                    if (prolongation.getLocation().getVehicule() != null) {
                        searchableText += prolongation.getLocation().getVehicule().getImmatriculation() + " ";
                    }
                }
                searchableText += prolongation.getMotifProlong() != null ? prolongation.getMotifProlong() : "";
                
                if (!searchableText.toLowerCase().contains(searchText)) {
                    matches = false;
                }
            }

            // Filtre par statut
            if (selectedStatut != null && prolongation.getStatut() != selectedStatut) {
                matches = false;
            }

            // Filtre par période
            if (dateDebut != null && prolongation.getDateDemandeProlong() != null &&
                prolongation.getDateDemandeProlong().isBefore(dateDebut)) {
                matches = false;
            }
            if (dateFin != null && prolongation.getDateDemandeProlong() != null &&
                prolongation.getDateDemandeProlong().isAfter(dateFin)) {
                matches = false;
            }

            if (matches) {
                filteredList.add(prolongation);
            }
        }

        prolongationTable.setItems(filteredList);
        lblTotalCount.setText("Total: " + filteredList.size());
    }

    private void calculerPrixProlong() {
        Location selectedLocation = locationComboBox.getValue();
        LocalDate nouvelleDateFin = nouvelleDateFinPicker.getValue();
        
        if (selectedLocation != null && nouvelleDateFin != null && 
            selectedLocation.getDateFinPrevue() != null && selectedLocation.getVehicule() != null) {
            
            long joursSupplementaires = nouvelleDateFin.toEpochDay() - 
                                      selectedLocation.getDateFinPrevue().toEpochDay();
            
            if (joursSupplementaires > 0) {
                double prixParJour = selectedLocation.getVehicule().getPrixParJour();
                double prixProlong = joursSupplementaires * prixParJour;
                prixProlongField.setText(String.format("%.2f", prixProlong));
            }
        }
    }

    @FXML
    private void handleNouvelleProlongation() {
        clearForm();
        isEditMode = false;
        selectedProlongation = null;
    }

    @FXML
    private void handleModifierProlongation() {
        Prolongation selected = prolongationTable.getSelectionModel().getSelectedItem();
        if (selected == null) {
            showAlert("Veuillez sélectionner une prolongation à modifier.", AlertType.WARNING);
            return;
        }

        if (selected.getStatut() != Prolongation.StatutProlong.DEMANDEE) {
            showAlert("Seules les prolongations en attente peuvent être modifiées.", AlertType.WARNING);
            return;
        }

        loadProlongationInForm(selected);
        isEditMode = true;
        selectedProlongation = selected;
    }

    @FXML
    private void handleSauvegarderProlongation() {
        if (!validateForm()) return;

        try {
            Prolongation prolongation = isEditMode ? selectedProlongation : new Prolongation();
            
            prolongation.setLocation(locationComboBox.getValue());
            prolongation.setNouvelleDateFin(nouvelleDateFinPicker.getValue());
            prolongation.setMotifProlong(motifField.getText());
            prolongation.setNotesAgent(notesArea.getText());
            prolongation.setPrixProlong(Double.parseDouble(prixProlongField.getText()));
            prolongation.setVerificationPermisValide(verificationPermisCheck.isSelected());
            prolongation.setVerificationAssuranceValide(verificationAssuranceCheck.isSelected());
            prolongation.setControleVehicule(controleVehiculeCheck.isSelected());
            
            if (!kilometrageField.getText().isEmpty()) {
                prolongation.setKilometrageActuel(Integer.parseInt(kilometrageField.getText()));
            }

            if (!isEditMode) {
                prolongation.setAncienneDateFin(locationComboBox.getValue().getDateFinPrevue());
                prolongation.setDateDemandeProlong(LocalDate.now());
            }

            prolongationDAO.save(prolongation);
            
            showAlert(isEditMode ? "Prolongation modifiée avec succès!" : "Prolongation créée avec succès!", 
                     AlertType.INFORMATION);
            
            clearForm();
            loadData();
            updateStatistics();
            
        } catch (Exception e) {
            showAlert("Erreur lors de la sauvegarde: " + e.getMessage(), AlertType.ERROR);
            e.printStackTrace();
        }
    }

    @FXML
    private void handleApprouverProlongation() {
        Prolongation selected = prolongationTable.getSelectionModel().getSelectedItem();
        if (selected == null) {
            showAlert("Veuillez sélectionner une prolongation à approuver.", AlertType.WARNING);
            return;
        }

        if (selected.getStatut() != Prolongation.StatutProlong.DEMANDEE) {
            showAlert("Seules les prolongations en attente peuvent être approuvées.", AlertType.WARNING);
            return;
        }

        // Vérifier les conditions d'approbation
        if (!selected.isVerificationPermisValide() || !selected.isVerificationAssuranceValide() || 
            !selected.isControleVehicule()) {
            showAlert("Toutes les vérifications réglementaires doivent être complétées avant l'approbation.", 
                     AlertType.WARNING);
            return;
        }

        Alert confirmAlert = new Alert(AlertType.CONFIRMATION);
        confirmAlert.setTitle("Confirmation d'approbation");
        confirmAlert.setHeaderText("Approuver la prolongation");
        confirmAlert.setContentText("Êtes-vous sûr de vouloir approuver cette prolongation?");

        Optional<ButtonType> result = confirmAlert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            try {
                String agentNom = LoginController.loggedInUser != null ? 
                    LoginController.loggedInUser.getUsername() : "Agent";
                selected.approuver(agentNom);
                prolongationDAO.save(selected);
                
                showAlert("Prolongation approuvée avec succès!", AlertType.INFORMATION);
                loadData();
                updateStatistics();
                
            } catch (Exception e) {
                showAlert("Erreur lors de l'approbation: " + e.getMessage(), AlertType.ERROR);
                e.printStackTrace();
            }
        }
    }

    @FXML
    private void handleRefuserProlongation() {
        Prolongation selected = prolongationTable.getSelectionModel().getSelectedItem();
        if (selected == null) {
            showAlert("Veuillez sélectionner une prolongation à refuser.", AlertType.WARNING);
            return;
        }

        if (selected.getStatut() != Prolongation.StatutProlong.DEMANDEE) {
            showAlert("Seules les prolongations en attente peuvent être refusées.", AlertType.WARNING);
            return;
        }

        TextInputDialog dialog = new TextInputDialog();
        dialog.setTitle("Refus de prolongation");
        dialog.setHeaderText("Motif du refus");
        dialog.setContentText("Veuillez indiquer le motif du refus:");

        Optional<String> result = dialog.showAndWait();
        if (result.isPresent()) {
            try {
                selected.setStatut(Prolongation.StatutProlong.REFUSEE);
                selected.setNotesAgent(result.get());
                prolongationDAO.save(selected);
                
                showAlert("Prolongation refusée.", AlertType.INFORMATION);
                loadData();
                updateStatistics();
                
            } catch (Exception e) {
                showAlert("Erreur lors du refus: " + e.getMessage(), AlertType.ERROR);
                e.printStackTrace();
            }
        }
    }

    private boolean validateForm() {
        if (locationComboBox.getValue() == null) {
            showAlert("Veuillez sélectionner une location.", AlertType.WARNING);
            return false;
        }
        
        if (nouvelleDateFinPicker.getValue() == null) {
            showAlert("Veuillez sélectionner la nouvelle date de fin.", AlertType.WARNING);
            return false;
        }
        
        if (nouvelleDateFinPicker.getValue().isBefore(LocalDate.now())) {
            showAlert("La nouvelle date de fin ne peut pas être dans le passé.", AlertType.WARNING);
            return false;
        }
        
        if (motifField.getText().trim().isEmpty()) {
            showAlert("Veuillez indiquer le motif de la prolongation.", AlertType.WARNING);
            return false;
        }
        
        if (prixProlongField.getText().trim().isEmpty()) {
            showAlert("Veuillez indiquer le prix de la prolongation.", AlertType.WARNING);
            return false;
        }
        
        try {
            Double.parseDouble(prixProlongField.getText());
        } catch (NumberFormatException e) {
            showAlert("Le prix doit être un nombre valide.", AlertType.WARNING);
            return false;
        }
        
        return true;
    }

    private void loadProlongationInForm(Prolongation prolongation) {
        locationComboBox.setValue(prolongation.getLocation());
        nouvelleDateFinPicker.setValue(prolongation.getNouvelleDateFin());
        motifField.setText(prolongation.getMotifProlong());
        notesArea.setText(prolongation.getNotesAgent());
        prixProlongField.setText(String.valueOf(prolongation.getPrixProlong()));
        verificationPermisCheck.setSelected(prolongation.isVerificationPermisValide());
        verificationAssuranceCheck.setSelected(prolongation.isVerificationAssuranceValide());
        controleVehiculeCheck.setSelected(prolongation.isControleVehicule());
        
        if (prolongation.getKilometrageActuel() != null) {
            kilometrageField.setText(String.valueOf(prolongation.getKilometrageActuel()));
        }
    }

    private void clearForm() {
        locationComboBox.setValue(null);
        nouvelleDateFinPicker.setValue(null);
        motifField.clear();
        notesArea.clear();
        prixProlongField.clear();
        kilometrageField.clear();
        verificationPermisCheck.setSelected(false);
        verificationAssuranceCheck.setSelected(false);
        controleVehiculeCheck.setSelected(false);
    }

    private void updateStatistics() {
        try {
            long total = prolongationDAO.compterProlongationsParStatut(null);
            long enAttente = prolongationDAO.compterProlongationsParStatut(Prolongation.StatutProlong.DEMANDEE);
            long approuvees = prolongationDAO.compterProlongationsParStatut(Prolongation.StatutProlong.APPROUVEE);
            long refusees = prolongationDAO.compterProlongationsParStatut(Prolongation.StatutProlong.REFUSEE);

            Platform.runLater(() -> {
                lblTotalCount.setText("Total: " + total);
                lblEnAttente.setText("En attente: " + enAttente);
                lblApprouvees.setText("Approuvées: " + approuvees);
                lblRefusees.setText("Refusées: " + refusees);
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String getStatutDisplayName(Prolongation.StatutProlong statut) {
        switch (statut) {
            case DEMANDEE: return "En attente";
            case APPROUVEE: return "Approuvée";
            case REFUSEE: return "Refusée";
            case ANNULEE: return "Annulée";
            case TERMINEE: return "Terminée";
            default: return statut.toString();
        }
    }

    private void showAlert(String message, AlertType type) {
        Alert alert = new Alert(type);
        alert.setTitle("Gestion des Prolongations");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    @FXML
    private void navigateToPage(ActionEvent event) {
        NavigationController.handleNavigation(event, (Stage) prolongationTable.getScene().getWindow());
    }

    @FXML
    private void handleLogout(ActionEvent event) {
        try {
            LoginController.loggedInUser = null;
            javafx.scene.Parent root = javafx.fxml.FXMLLoader.load(getClass().getResource("/view/login.fxml"));
            Stage stage = (Stage) prolongationTable.getScene().getWindow();
            stage.setScene(new javafx.scene.Scene(root));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
