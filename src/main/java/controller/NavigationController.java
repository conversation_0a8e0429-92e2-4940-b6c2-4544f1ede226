package controller;

import javafx.event.ActionEvent;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.control.MenuItem;
import javafx.scene.control.Alert;
import javafx.stage.Stage;

public class NavigationController {
    public static void handleNavigation(ActionEvent event, Stage stage) {
        if (!(event.getSource() instanceof Button)) return;
        Button btn = (Button) event.getSource();
        String id = btn.getId();
        String fxml = null;
        switch (id) {
            case "btnDashboard":
                fxml = "/view/dashboard.fxml";
                break;
            case "btnClients":
                fxml = "/view/client.fxml";
                break;
            case "btnVehicules":
                fxml = "/view/vehicule.fxml";
                break;
            case "btnCatalogue":
                fxml = "/view/catalogue.fxml";
                break;
            case "btnLocations":
                fxml = "/view/location.fxml";
                break;
            case "btnPaiements":
                fxml = "/view/paiement.fxml";
                break;
            case "btnUserManagement":
                fxml = "/view/user_management.fxml";
                break;
            case "btnStats": // Example for a new page
                fxml = "/view/statistiques.fxml";
                break;
            // To add a new page, add a new case here with the button's fx:id and the FXML path
            default:
                return;
        }
        try {
            Parent root = FXMLLoader.load(NavigationController.class.getResource(fxml));
            stage.setScene(new Scene(root));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Navigation par MenuItem (pour les menus des nouvelles pages)
     */
    public static void handleNavigation(ActionEvent event, Stage currentStage) {
        if (event.getSource() instanceof MenuItem) {
            MenuItem menuItem = (MenuItem) event.getSource();
            String page = (String) menuItem.getUserData();

            try {
                String fxmlPath = "/view/" + page + ".fxml";

                // Gestion spéciale pour certaines pages
                if ("historique_vehicule".equals(page)) {
                    fxmlPath = "/view/historique_vehicule.fxml";
                }

                Parent root = FXMLLoader.load(NavigationController.class.getResource(fxmlPath));
                Scene scene = new Scene(root);
                currentStage.setScene(scene);
                currentStage.show();
            } catch (Exception e) {
                e.printStackTrace();
                Alert alert = new Alert(Alert.AlertType.ERROR);
                alert.setTitle("Erreur de navigation");
                alert.setHeaderText("Impossible de charger la page");
                alert.setContentText("Page: " + page + "\nErreur: " + e.getMessage());
                alert.showAndWait();
            }
        } else if (event.getSource() instanceof Button) {
            // Déléguer à la méthode existante pour les boutons
            handleNavigation(event, currentStage);
        }
    }
}