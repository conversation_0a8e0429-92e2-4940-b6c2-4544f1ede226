package controller;

import javafx.event.ActionEvent;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.control.MenuItem;
import javafx.scene.control.Alert;
import javafx.stage.Stage;

public class NavigationController {

    public static void handleNavigation(ActionEvent event, Stage currentStage) {
        if (event.getSource() instanceof MenuItem) {
            MenuItem menuItem = (MenuItem) event.getSource();
            String page = (String) menuItem.getUserData();

            try {
                String fxmlPath = "/view/" + page + ".fxml";

                // Gestion spéciale pour certaines pages
                if ("historique_vehicule".equals(page)) {
                    fxmlPath = "/view/historique_vehicule.fxml";
                }

                Parent root = FXMLLoader.load(NavigationController.class.getResource(fxmlPath));
                Scene scene = new Scene(root);
                currentStage.setScene(scene);
                currentStage.show();
            } catch (Exception e) {
                e.printStackTrace();
                Alert alert = new Alert(Alert.AlertType.ERROR);
                alert.setTitle("Erreur de navigation");
                alert.setHeaderText("Impossible de charger la page");
                alert.setContentText("Page: " + page + "\nErreur: " + e.getMessage());
                alert.showAndWait();
            }
        } else if (event.getSource() instanceof Button) {
            // Déléguer à la méthode existante pour les boutons
            handleNavigation(event, currentStage);
        }
    }
}