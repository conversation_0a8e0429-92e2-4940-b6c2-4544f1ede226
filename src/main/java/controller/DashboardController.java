package controller;

import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Node;
import javafx.scene.layout.StackPane;
import javafx.stage.Stage;
import javafx.scene.Scene;
import javafx.scene.Parent;
import javafx.event.ActionEvent;
import java.io.IOException;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.TableView;
import javafx.scene.control.TableColumn;
import javafx.scene.control.Alert;
import model.User;
import model.Agent;
import model.Client;
import model.Vehicule;
import model.Location;
import model.Paiement;
import dao.ClientDAO;
import dao.VehiculeDAO;
import dao.LocationDAO;
import dao.PaiementDAO;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import javafx.scene.chart.PieChart;
import javafx.scene.chart.BarChart;
import javafx.scene.chart.CategoryAxis;
import javafx.scene.chart.NumberAxis;
import javafx.scene.chart.XYChart;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import controller.LoginController;

public class DashboardController {
    @FXML
    private StackPane contentPane;

    @FXML private Button btnVehicules;
    @FXML private Button btnUserManagement;
    @FXML private Button btnClients;
    @FXML private Button btnCatalogue;
    @FXML private Button btnLocations;
    @FXML private Button btnPaiements;
    @FXML private Button btnDashboard;
    @FXML private Button btnRentalHistory;
    @FXML private Button btnPaymentHistory;
    @FXML private Button btnNewLocation;
    
    // Remove all FXML fields and methods related to dashboard_content.fxml (stats, charts, recent activity)
    // Only keep navigation/sidebar logic and contentPane reference

    @FXML
    public void initialize() {
        Object user = LoginController.loggedInUser;
        // Example: role-based UI logic
        if (user instanceof Agent) {
            if (btnVehicules != null) btnVehicules.setDisable(true);
            if (btnUserManagement != null) btnUserManagement.setVisible(false);
        }
        // Load dashboard content immediately on startup
        showDashboard();
    }

    // Remove all FXML fields and methods related to dashboard_content.fxml (stats, charts, recent activity)
    // Only keep navigation/sidebar logic and contentPane reference

    @FXML
    private void showVehicules() {
        loadView("/view/vehicule.fxml");
    }

    @FXML
    private void showClients() {
        loadView("/view/client.fxml");
    }

    @FXML
    private void showCatalogue() {
        loadView("/view/catalogue.fxml");
    }

    @FXML
    private void showLocations() {
        loadView("/view/location.fxml");
    }

    @FXML
    private void showPaiements() {
        loadView("/view/paiement.fxml");
    }

    @FXML
    private void showUserManagement() {
        loadView("/view/user_management.fxml");
    }

    @FXML
    private void showDashboard() {
        try {
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/view/dashboard_content.fxml"));
            javafx.scene.Node node = loader.load();
            if (contentPane != null) {
                contentPane.getChildren().setAll(node);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @FXML
    private void showRentalHistory() {
        loadView("/view/historique_rentals.fxml");
    }
    @FXML
    private void showPaymentHistory() {
        loadView("/view/historique_paiements.fxml");
    }

    @FXML
    private void showNewLocation() {
        loadView("/view/location_create.fxml");
    }

    // Remove handleRefreshData and any references to loadDashboardStats or loadDashboardContent

    @FXML
    public void handleExportData() {
        // TODO: Implement export logic
        System.out.println("Export functionality to be implemented");
    }

    @FXML
    private void navigateToPage(ActionEvent event) {
        if (event.getSource() instanceof Button) {
            Button btn = (Button) event.getSource();
            String id = btn.getId();
            if (id == null) return;
            
            switch (id) {
                case "btnDashboard":
                    showDashboard();
                    break;
                case "btnClients":
                    showClients();
                    break;
                case "btnVehicules":
                    showVehicules();
                    break;
                case "btnCatalogue":
                    showCatalogue();
                    break;
                case "btnLocations":
                    showLocations();
                    break;
                case "btnPaiements":
                    showPaiements();
                    break;
                case "btnUserManagement":
                    showUserManagement();
                    break;
                case "btnRentalHistory":
                    showRentalHistory();
                    break;
                case "btnPaymentHistory":
                    showPaymentHistory();
                    break;
                case "btnNewLocation":
                    showNewLocation();
                    break;
            }
        }
    }

    @FXML
    private void handleLogout(ActionEvent event) {
        try {
            // Clear logged in user
            LoginController.loggedInUser = null;
            
            Stage stage = (Stage) contentPane.getScene().getWindow();
            Parent root = FXMLLoader.load(getClass().getResource("/view/login.fxml"));
            stage.setScene(new Scene(root));
            stage.setTitle("LocationV1 - Connexion");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * Navigation vers les nouvelles pages (ouvre dans une nouvelle fenêtre)
     */
    @FXML
    private void navigateToNewPage(ActionEvent event) {
        try {
            Button button = (Button) event.getSource();
            String page = (String) button.getUserData();

            String fxmlPath = "/view/" + page + ".fxml";

            // Gestion spéciale pour l'historique des véhicules
            if ("historique_vehicule".equals(page)) {
                fxmlPath = "/view/historique_vehicule.fxml";
            }

            Parent root = FXMLLoader.load(getClass().getResource(fxmlPath));
            Stage newStage = new Stage();
            newStage.setScene(new Scene(root));

            // Titre de la fenêtre selon la page
            switch (page) {
                case "prolongation":
                    newStage.setTitle("LocationV1 - Gestion des Prolongations");
                    break;
                case "maintenance":
                    newStage.setTitle("LocationV1 - Gestion de la Maintenance");
                    break;
                case "accident":
                    newStage.setTitle("LocationV1 - Gestion des Accidents");
                    break;
                case "historique_vehicule":
                    newStage.setTitle("LocationV1 - Historique des Véhicules");
                    break;
                default:
                    newStage.setTitle("LocationV1 - " + page);
            }

            newStage.show();
        } catch (Exception e) {
            e.printStackTrace();
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Erreur de navigation");
            alert.setHeaderText("Impossible de charger la page");
            alert.setContentText("Erreur: " + e.getMessage());
            alert.showAndWait();
        }
    }

    private void loadView(String fxmlPath) {
        if (contentPane == null) {
            System.err.println("Error: contentPane is null. FXML injection may have failed.");
            return;
        }
        try {
            Node node = FXMLLoader.load(getClass().getResource(fxmlPath));
            contentPane.getChildren().setAll(node);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
} 