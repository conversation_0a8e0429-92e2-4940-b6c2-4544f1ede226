package controller;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.stage.Stage;
import javafx.event.ActionEvent;
import javafx.scene.control.Alert.AlertType;
import javafx.application.Platform;
import javafx.util.StringConverter;

import dao.MaintenanceDAO;
import dao.VehiculeDAO;
import model.Maintenance;
import model.Vehicule;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

/**
 * Contrôleur pour la gestion de la maintenance des véhicules
 * Conforme aux standards de maintenance des flottes au Maroc
 */
public class MaintenanceController {

    @FXML private TableView<Maintenance> maintenanceTable;
    @FXML private TableColumn<Maintenance, Long> idColumn;
    @FXML private TableColumn<Maintenance, String> vehiculeColumn;
    @FXML private TableColumn<Maintenance, String> typeColumn;
    @FXML private TableColumn<Maintenance, String> statutColumn;
    @FXML private TableColumn<Maintenance, String> dateProgrammeeColumn;
    @FXML private TableColumn<Maintenance, String> prioriteColumn;
    @FXML private TableColumn<Maintenance, Double> coutColumn;
    @FXML private TableColumn<Maintenance, String> garageColumn;

    @FXML private TextField searchField;
    @FXML private ComboBox<Maintenance.StatutMaintenance> statutFilter;
    @FXML private ComboBox<Maintenance.TypeMaintenance> typeFilter;
    @FXML private ComboBox<Maintenance.NiveauPriorite> prioriteFilter;
    @FXML private DatePicker dateDebutFilter;
    @FXML private DatePicker dateFinFilter;

    @FXML private Label lblTotalCount;
    @FXML private Label lblProgrammees;
    @FXML private Label lblEnCours;
    @FXML private Label lblTerminees;
    @FXML private Label lblUrgentes;

    // Formulaire de maintenance
    @FXML private ComboBox<Vehicule> vehiculeComboBox;
    @FXML private ComboBox<Maintenance.TypeMaintenance> typeMaintenanceComboBox;
    @FXML private ComboBox<Maintenance.NiveauPriorite> prioriteComboBox;
    @FXML private DatePicker dateProgrammeePicker;
    @FXML private TextField descriptionField;
    @FXML private TextArea observationsAvantArea;
    @FXML private TextField kilometrageField;
    @FXML private TextField intervalleKmField;
    @FXML private TextField nomGarageField;
    @FXML private TextField telephoneGarageField;
    @FXML private TextArea adresseGarageArea;
    @FXML private TextField technicienField;
    @FXML private CheckBox vehiculeImmobiliseCheck;
    @FXML private TextField dureeEstimeeField;

    // Champs pour maintenance terminée
    @FXML private TextArea travauEffectuesArea;
    @FXML private TextArea piecesChangeesArea;
    @FXML private TextArea observationsApresArea;
    @FXML private TextField coutMainOeuvreField;
    @FXML private TextField coutPiecesField;
    @FXML private TextField numeroFactureField;
    @FXML private CheckBox facturePayeeCheck;
    @FXML private CheckBox conformiteCheck;

    private MaintenanceDAO maintenanceDAO;
    private VehiculeDAO vehiculeDAO;
    private ObservableList<Maintenance> maintenanceList;
    private ObservableList<Vehicule> vehiculeList;

    private boolean isEditMode = false;
    private Maintenance selectedMaintenance = null;

    @FXML
    public void initialize() {
        maintenanceDAO = new MaintenanceDAO();
        vehiculeDAO = new VehiculeDAO();

        setupTableColumns();
        setupFilters();
        setupFormControls();
        loadData();
        updateStatistics();
    }

    private void setupTableColumns() {
        idColumn.setCellValueFactory(new PropertyValueFactory<>("id"));
        
        vehiculeColumn.setCellValueFactory(data -> {
            Vehicule v = data.getValue().getVehicule();
            return new javafx.beans.property.SimpleStringProperty(
                v != null ? v.getMarque() + " " + v.getModele() + " (" + v.getImmatriculation() + ")" : "");
        });
        
        typeColumn.setCellValueFactory(data -> {
            Maintenance.TypeMaintenance type = data.getValue().getTypeMaintenance();
            return new javafx.beans.property.SimpleStringProperty(
                type != null ? getTypeDisplayName(type) : "");
        });
        
        statutColumn.setCellValueFactory(data -> {
            Maintenance.StatutMaintenance statut = data.getValue().getStatut();
            return new javafx.beans.property.SimpleStringProperty(
                statut != null ? getStatutDisplayName(statut) : "");
        });
        
        dateProgrammeeColumn.setCellValueFactory(data -> {
            LocalDate date = data.getValue().getDateProgrammee();
            return new javafx.beans.property.SimpleStringProperty(
                date != null ? date.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "");
        });
        
        prioriteColumn.setCellValueFactory(data -> {
            Maintenance.NiveauPriorite priorite = data.getValue().getPriorite();
            return new javafx.beans.property.SimpleStringProperty(
                priorite != null ? getPrioriteDisplayName(priorite) : "");
        });
        
        coutColumn.setCellValueFactory(new PropertyValueFactory<>("coutTotal"));
        coutColumn.setCellFactory(col -> new TableCell<Maintenance, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(String.format("%.2f DH", item));
                }
            }
        });
        
        garageColumn.setCellValueFactory(new PropertyValueFactory<>("nomGarage"));

        // Style des cellules selon la priorité et le statut
        maintenanceTable.setRowFactory(tv -> {
            TableRow<Maintenance> row = new TableRow<>();
            row.itemProperty().addListener((obs, oldItem, newItem) -> {
                if (newItem == null) {
                    row.setStyle("");
                } else {
                    String style = "";
                    
                    // Couleur selon la priorité
                    switch (newItem.getPriorite()) {
                        case CRITIQUE:
                        case URGENTE:
                            style = "-fx-background-color: #f8d7da;";
                            break;
                        case ELEVEE:
                            style = "-fx-background-color: #fff3cd;";
                            break;
                        default:
                            // Couleur selon le statut
                            switch (newItem.getStatut()) {
                                case EN_COURS:
                                    style = "-fx-background-color: #cce5ff;";
                                    break;
                                case TERMINEE:
                                    style = "-fx-background-color: #d4edda;";
                                    break;
                                case ANNULEE:
                                    style = "-fx-background-color: #e2e3e5;";
                                    break;
                            }
                    }
                    
                    // Maintenance en retard
                    if (newItem.estEnRetard()) {
                        style = "-fx-background-color: #ff6b6b; -fx-text-fill: white;";
                    }
                    
                    row.setStyle(style);
                }
            });
            return row;
        });
    }

    private void setupFilters() {
        // Configuration des ComboBox de filtres
        statutFilter.setItems(FXCollections.observableArrayList(Maintenance.StatutMaintenance.values()));
        statutFilter.setConverter(new StringConverter<Maintenance.StatutMaintenance>() {
            @Override
            public String toString(Maintenance.StatutMaintenance statut) {
                return statut != null ? getStatutDisplayName(statut) : "";
            }
            @Override
            public Maintenance.StatutMaintenance fromString(String string) { return null; }
        });

        typeFilter.setItems(FXCollections.observableArrayList(Maintenance.TypeMaintenance.values()));
        typeFilter.setConverter(new StringConverter<Maintenance.TypeMaintenance>() {
            @Override
            public String toString(Maintenance.TypeMaintenance type) {
                return type != null ? getTypeDisplayName(type) : "";
            }
            @Override
            public Maintenance.TypeMaintenance fromString(String string) { return null; }
        });

        prioriteFilter.setItems(FXCollections.observableArrayList(Maintenance.NiveauPriorite.values()));
        prioriteFilter.setConverter(new StringConverter<Maintenance.NiveauPriorite>() {
            @Override
            public String toString(Maintenance.NiveauPriorite priorite) {
                return priorite != null ? getPrioriteDisplayName(priorite) : "";
            }
            @Override
            public Maintenance.NiveauPriorite fromString(String string) { return null; }
        });

        // Listeners pour les filtres
        searchField.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        statutFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        typeFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        prioriteFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        dateDebutFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        dateFinFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
    }

    private void setupFormControls() {
        // Configuration du ComboBox des véhicules
        vehiculeList = FXCollections.observableArrayList();
        vehiculeComboBox.setItems(vehiculeList);
        vehiculeComboBox.setConverter(new StringConverter<Vehicule>() {
            @Override
            public String toString(Vehicule vehicule) {
                if (vehicule == null) return "";
                return String.format("%s %s (%s)", 
                    vehicule.getMarque(), vehicule.getModele(), vehicule.getImmatriculation());
            }
            @Override
            public Vehicule fromString(String string) { return null; }
        });

        // Configuration des autres ComboBox
        typeMaintenanceComboBox.setItems(FXCollections.observableArrayList(Maintenance.TypeMaintenance.values()));
        typeMaintenanceComboBox.setConverter(new StringConverter<Maintenance.TypeMaintenance>() {
            @Override
            public String toString(Maintenance.TypeMaintenance type) {
                return type != null ? getTypeDisplayName(type) : "";
            }
            @Override
            public Maintenance.TypeMaintenance fromString(String string) { return null; }
        });

        prioriteComboBox.setItems(FXCollections.observableArrayList(Maintenance.NiveauPriorite.values()));
        prioriteComboBox.setConverter(new StringConverter<Maintenance.NiveauPriorite>() {
            @Override
            public String toString(Maintenance.NiveauPriorite priorite) {
                return priorite != null ? getPrioriteDisplayName(priorite) : "";
            }
            @Override
            public Maintenance.NiveauPriorite fromString(String string) { return null; }
        });

        // Validation des champs numériques
        setupNumericField(kilometrageField);
        setupNumericField(intervalleKmField);
        setupNumericField(dureeEstimeeField);
        setupDecimalField(coutMainOeuvreField);
        setupDecimalField(coutPiecesField);

        // Calcul automatique du coût total
        coutMainOeuvreField.textProperty().addListener((obs, oldVal, newVal) -> calculerCoutTotal());
        coutPiecesField.textProperty().addListener((obs, oldVal, newVal) -> calculerCoutTotal());

        // Remplissage automatique du kilométrage
        vehiculeComboBox.valueProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal != null && newVal.getMetrage() != null) {
                kilometrageField.setText(String.valueOf(newVal.getMetrage()));
            }
        });
    }

    private void setupNumericField(TextField field) {
        field.textProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal.matches("\\d*")) {
                field.setText(oldVal);
            }
        });
    }

    private void setupDecimalField(TextField field) {
        field.textProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal.matches("\\d*(\\.\\d*)?")) {
                field.setText(oldVal);
            }
        });
    }

    private void calculerCoutTotal() {
        try {
            double mainOeuvre = coutMainOeuvreField.getText().isEmpty() ? 0 : 
                               Double.parseDouble(coutMainOeuvreField.getText());
            double pieces = coutPiecesField.getText().isEmpty() ? 0 : 
                           Double.parseDouble(coutPiecesField.getText());
            // Le coût total sera calculé automatiquement dans le modèle
        } catch (NumberFormatException e) {
            // Ignore les erreurs de format pendant la saisie
        }
    }

    private void loadData() {
        try {
            // Charger les maintenances
            List<Maintenance> maintenances = maintenanceDAO.findAll();
            maintenanceList = FXCollections.observableArrayList(maintenances);
            maintenanceTable.setItems(maintenanceList);

            // Charger les véhicules
            List<Vehicule> vehicules = vehiculeDAO.findAll();
            vehiculeList.setAll(vehicules);

        } catch (Exception e) {
            showAlert("Erreur lors du chargement des données: " + e.getMessage(), AlertType.ERROR);
            e.printStackTrace();
        }
    }

    private void applyFilters() {
        if (maintenanceList == null) return;

        ObservableList<Maintenance> filteredList = FXCollections.observableArrayList();
        String searchText = searchField.getText().toLowerCase();
        Maintenance.StatutMaintenance selectedStatut = statutFilter.getValue();
        Maintenance.TypeMaintenance selectedType = typeFilter.getValue();
        Maintenance.NiveauPriorite selectedPriorite = prioriteFilter.getValue();
        LocalDate dateDebut = dateDebutFilter.getValue();
        LocalDate dateFin = dateFinFilter.getValue();

        for (Maintenance maintenance : maintenanceList) {
            boolean matches = true;

            // Filtre par texte de recherche
            if (!searchText.isEmpty()) {
                String searchableText = "";
                if (maintenance.getVehicule() != null) {
                    searchableText += maintenance.getVehicule().getImmatriculation() + " " +
                                    maintenance.getVehicule().getMarque() + " " +
                                    maintenance.getVehicule().getModele() + " ";
                }
                searchableText += maintenance.getDescription() != null ? maintenance.getDescription() : "";
                searchableText += maintenance.getNomGarage() != null ? maintenance.getNomGarage() : "";
                
                if (!searchableText.toLowerCase().contains(searchText)) {
                    matches = false;
                }
            }

            // Filtres par énumérations
            if (selectedStatut != null && maintenance.getStatut() != selectedStatut) {
                matches = false;
            }
            if (selectedType != null && maintenance.getTypeMaintenance() != selectedType) {
                matches = false;
            }
            if (selectedPriorite != null && maintenance.getPriorite() != selectedPriorite) {
                matches = false;
            }

            // Filtre par période
            if (dateDebut != null && maintenance.getDateProgrammee() != null &&
                maintenance.getDateProgrammee().isBefore(dateDebut)) {
                matches = false;
            }
            if (dateFin != null && maintenance.getDateProgrammee() != null &&
                maintenance.getDateProgrammee().isAfter(dateFin)) {
                matches = false;
            }

            if (matches) {
                filteredList.add(maintenance);
            }
        }

        maintenanceTable.setItems(filteredList);
        lblTotalCount.setText("Total: " + filteredList.size());
    }

    @FXML
    private void handleNouvelleMaintenance() {
        clearForm();
        isEditMode = false;
        selectedMaintenance = null;
        dateProgrammeePicker.setValue(LocalDate.now());
        prioriteComboBox.setValue(Maintenance.NiveauPriorite.NORMALE);
    }

    @FXML
    private void handleModifierMaintenance() {
        Maintenance selected = maintenanceTable.getSelectionModel().getSelectedItem();
        if (selected == null) {
            showAlert("Veuillez sélectionner une maintenance à modifier.", AlertType.WARNING);
            return;
        }

        loadMaintenanceInForm(selected);
        isEditMode = true;
        selectedMaintenance = selected;
    }

    @FXML
    private void handleSauvegarderMaintenance() {
        if (!validateForm()) return;

        try {
            Maintenance maintenance = isEditMode ? selectedMaintenance : new Maintenance();
            
            maintenance.setVehicule(vehiculeComboBox.getValue());
            maintenance.setTypeMaintenance(typeMaintenanceComboBox.getValue());
            maintenance.setPriorite(prioriteComboBox.getValue());
            maintenance.setDateProgrammee(dateProgrammeePicker.getValue());
            maintenance.setDescription(descriptionField.getText());
            maintenance.setObservationsAvant(observationsAvantArea.getText());
            
            if (!kilometrageField.getText().isEmpty()) {
                maintenance.setKilometrageActuel(Integer.parseInt(kilometrageField.getText()));
            }
            if (!intervalleKmField.getText().isEmpty()) {
                maintenance.setIntervalleKilometrage(Integer.parseInt(intervalleKmField.getText()));
            }
            if (!dureeEstimeeField.getText().isEmpty()) {
                maintenance.setDureeEstimeeJours(Integer.parseInt(dureeEstimeeField.getText()));
            }
            
            maintenance.setNomGarage(nomGarageField.getText());
            maintenance.setTelephoneGarage(telephoneGarageField.getText());
            maintenance.setAdresseGarage(adresseGarageArea.getText());
            maintenance.setTechnicienResponsable(technicienField.getText());
            maintenance.setVehiculeImmobilise(vehiculeImmobiliseCheck.isSelected());
            
            // Champs pour maintenance terminée
            maintenance.setTravauEffectues(travauEffectuesArea.getText());
            maintenance.setPiecesChangees(piecesChangeesArea.getText());
            maintenance.setObservationsApres(observationsApresArea.getText());
            
            if (!coutMainOeuvreField.getText().isEmpty()) {
                maintenance.setCoutMainOeuvre(Double.parseDouble(coutMainOeuvreField.getText()));
            }
            if (!coutPiecesField.getText().isEmpty()) {
                maintenance.setCoutPieces(Double.parseDouble(coutPiecesField.getText()));
            }
            
            maintenance.setNumeroFacture(numeroFactureField.getText());
            maintenance.setFacturePayee(facturePayeeCheck.isSelected());
            maintenance.setConformiteReglementaire(conformiteCheck.isSelected());

            if (!isEditMode) {
                String agentNom = LoginController.loggedInUser != null ? 
                    LoginController.loggedInUser.getUsername() : "Agent";
                maintenance.setCreatedBy(agentNom);
            }

            maintenanceDAO.save(maintenance);
            
            showAlert(isEditMode ? "Maintenance modifiée avec succès!" : "Maintenance créée avec succès!", 
                     AlertType.INFORMATION);
            
            clearForm();
            loadData();
            updateStatistics();
            
        } catch (Exception e) {
            showAlert("Erreur lors de la sauvegarde: " + e.getMessage(), AlertType.ERROR);
            e.printStackTrace();
        }
    }

    @FXML
    private void handleCommencerMaintenance() {
        Maintenance selected = maintenanceTable.getSelectionModel().getSelectedItem();
        if (selected == null) {
            showAlert("Veuillez sélectionner une maintenance à commencer.", AlertType.WARNING);
            return;
        }

        if (selected.getStatut() != Maintenance.StatutMaintenance.PROGRAMMEE) {
            showAlert("Seules les maintenances programmées peuvent être commencées.", AlertType.WARNING);
            return;
        }

        try {
            maintenanceDAO.updateStatut(selected.getId(), Maintenance.StatutMaintenance.EN_COURS);
            showAlert("Maintenance commencée!", AlertType.INFORMATION);
            loadData();
            updateStatistics();
        } catch (Exception e) {
            showAlert("Erreur: " + e.getMessage(), AlertType.ERROR);
        }
    }

    @FXML
    private void handleTerminerMaintenance() {
        Maintenance selected = maintenanceTable.getSelectionModel().getSelectedItem();
        if (selected == null) {
            showAlert("Veuillez sélectionner une maintenance à terminer.", AlertType.WARNING);
            return;
        }

        if (selected.getStatut() != Maintenance.StatutMaintenance.EN_COURS) {
            showAlert("Seules les maintenances en cours peuvent être terminées.", AlertType.WARNING);
            return;
        }

        // Dialog pour saisir les détails de fin
        Dialog<ButtonType> dialog = new Dialog<>();
        dialog.setTitle("Terminer la maintenance");
        dialog.setHeaderText("Détails de fin de maintenance");

        GridPane grid = new GridPane();
        grid.setHgap(10);
        grid.setVgap(10);
        grid.setPadding(new Insets(20, 150, 10, 10));

        TextArea travauArea = new TextArea();
        travauArea.setPromptText("Travaux effectués...");
        TextArea piecesArea = new TextArea();
        piecesArea.setPromptText("Pièces changées...");
        TextArea observationsArea = new TextArea();
        observationsArea.setPromptText("Observations...");

        grid.add(new Label("Travaux effectués:"), 0, 0);
        grid.add(travauArea, 1, 0);
        grid.add(new Label("Pièces changées:"), 0, 1);
        grid.add(piecesArea, 1, 1);
        grid.add(new Label("Observations:"), 0, 2);
        grid.add(observationsArea, 1, 2);

        dialog.getDialogPane().setContent(grid);
        dialog.getDialogPane().getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);

        Optional<ButtonType> result = dialog.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            try {
                selected.terminerMaintenance(
                    travauArea.getText(),
                    piecesArea.getText(),
                    observationsArea.getText()
                );
                maintenanceDAO.save(selected);
                
                showAlert("Maintenance terminée avec succès!", AlertType.INFORMATION);
                loadData();
                updateStatistics();
            } catch (Exception e) {
                showAlert("Erreur: " + e.getMessage(), AlertType.ERROR);
            }
        }
    }

    private boolean validateForm() {
        if (vehiculeComboBox.getValue() == null) {
            showAlert("Veuillez sélectionner un véhicule.", AlertType.WARNING);
            return false;
        }
        
        if (typeMaintenanceComboBox.getValue() == null) {
            showAlert("Veuillez sélectionner le type de maintenance.", AlertType.WARNING);
            return false;
        }
        
        if (dateProgrammeePicker.getValue() == null) {
            showAlert("Veuillez sélectionner la date programmée.", AlertType.WARNING);
            return false;
        }
        
        if (descriptionField.getText().trim().isEmpty()) {
            showAlert("Veuillez saisir une description.", AlertType.WARNING);
            return false;
        }
        
        return true;
    }

    private void loadMaintenanceInForm(Maintenance maintenance) {
        vehiculeComboBox.setValue(maintenance.getVehicule());
        typeMaintenanceComboBox.setValue(maintenance.getTypeMaintenance());
        prioriteComboBox.setValue(maintenance.getPriorite());
        dateProgrammeePicker.setValue(maintenance.getDateProgrammee());
        descriptionField.setText(maintenance.getDescription());
        observationsAvantArea.setText(maintenance.getObservationsAvant());
        
        if (maintenance.getKilometrageActuel() != null) {
            kilometrageField.setText(String.valueOf(maintenance.getKilometrageActuel()));
        }
        if (maintenance.getIntervalleKilometrage() != null) {
            intervalleKmField.setText(String.valueOf(maintenance.getIntervalleKilometrage()));
        }
        if (maintenance.getDureeEstimeeJours() != null) {
            dureeEstimeeField.setText(String.valueOf(maintenance.getDureeEstimeeJours()));
        }
        
        nomGarageField.setText(maintenance.getNomGarage());
        telephoneGarageField.setText(maintenance.getTelephoneGarage());
        adresseGarageArea.setText(maintenance.getAdresseGarage());
        technicienField.setText(maintenance.getTechnicienResponsable());
        vehiculeImmobiliseCheck.setSelected(maintenance.isVehiculeImmobilise());
        
        travauEffectuesArea.setText(maintenance.getTravauEffectues());
        piecesChangeesArea.setText(maintenance.getPiecesChangees());
        observationsApresArea.setText(maintenance.getObservationsApres());
        
        if (maintenance.getCoutMainOeuvre() != null) {
            coutMainOeuvreField.setText(String.valueOf(maintenance.getCoutMainOeuvre()));
        }
        if (maintenance.getCoutPieces() != null) {
            coutPiecesField.setText(String.valueOf(maintenance.getCoutPieces()));
        }
        
        numeroFactureField.setText(maintenance.getNumeroFacture());
        facturePayeeCheck.setSelected(maintenance.isFacturePayee());
        conformiteCheck.setSelected(maintenance.isConformiteReglementaire());
    }

    private void clearForm() {
        vehiculeComboBox.setValue(null);
        typeMaintenanceComboBox.setValue(null);
        prioriteComboBox.setValue(null);
        dateProgrammeePicker.setValue(null);
        descriptionField.clear();
        observationsAvantArea.clear();
        kilometrageField.clear();
        intervalleKmField.clear();
        dureeEstimeeField.clear();
        nomGarageField.clear();
        telephoneGarageField.clear();
        adresseGarageArea.clear();
        technicienField.clear();
        vehiculeImmobiliseCheck.setSelected(false);
        travauEffectuesArea.clear();
        piecesChangeesArea.clear();
        observationsApresArea.clear();
        coutMainOeuvreField.clear();
        coutPiecesField.clear();
        numeroFactureField.clear();
        facturePayeeCheck.setSelected(false);
        conformiteCheck.setSelected(false);
    }

    private void updateStatistics() {
        try {
            long total = maintenanceDAO.compterMaintenancesParStatut(null);
            long programmees = maintenanceDAO.compterMaintenancesParStatut(Maintenance.StatutMaintenance.PROGRAMMEE);
            long enCours = maintenanceDAO.compterMaintenancesParStatut(Maintenance.StatutMaintenance.EN_COURS);
            long terminees = maintenanceDAO.compterMaintenancesParStatut(Maintenance.StatutMaintenance.TERMINEE);
            List<Maintenance> urgentes = maintenanceDAO.findMaintenancesUrgentes();

            Platform.runLater(() -> {
                lblTotalCount.setText("Total: " + total);
                lblProgrammees.setText("Programmées: " + programmees);
                lblEnCours.setText("En cours: " + enCours);
                lblTerminees.setText("Terminées: " + terminees);
                lblUrgentes.setText("Urgentes: " + urgentes.size());
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // Méthodes utilitaires pour les noms d'affichage
    private String getTypeDisplayName(Maintenance.TypeMaintenance type) {
        switch (type) {
            case PREVENTIVE: return "Préventive";
            case CORRECTIVE: return "Corrective";
            case CONTROLE_TECHNIQUE: return "Contrôle technique";
            case VIDANGE: return "Vidange";
            case REVISION: return "Révision";
            case REPARATION: return "Réparation";
            case CHANGEMENT_PNEUS: return "Changement pneus";
            case FREINAGE: return "Freinage";
            case CLIMATISATION: return "Climatisation";
            case CARROSSERIE: return "Carrosserie";
            case ELECTRICITE: return "Électricité";
            case URGENCE: return "Urgence";
            default: return type.toString();
        }
    }

    private String getStatutDisplayName(Maintenance.StatutMaintenance statut) {
        switch (statut) {
            case PROGRAMMEE: return "Programmée";
            case EN_COURS: return "En cours";
            case TERMINEE: return "Terminée";
            case ANNULEE: return "Annulée";
            case REPORTEE: return "Reportée";
            case EN_ATTENTE_PIECES: return "En attente pièces";
            default: return statut.toString();
        }
    }

    private String getPrioriteDisplayName(Maintenance.NiveauPriorite priorite) {
        switch (priorite) {
            case FAIBLE: return "Faible";
            case NORMALE: return "Normale";
            case ELEVEE: return "Élevée";
            case CRITIQUE: return "Critique";
            case URGENTE: return "Urgente";
            default: return priorite.toString();
        }
    }

    private void showAlert(String message, AlertType type) {
        Alert alert = new Alert(type);
        alert.setTitle("Gestion de la Maintenance");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    @FXML
    private void navigateToPage(ActionEvent event) {
        NavigationController.handleNavigation(event, (Stage) maintenanceTable.getScene().getWindow());
    }

    @FXML
    private void handleLogout(ActionEvent event) {
        try {
            LoginController.loggedInUser = null;
            javafx.scene.Parent root = javafx.fxml.FXMLLoader.load(getClass().getResource("/view/login.fxml"));
            Stage stage = (Stage) maintenanceTable.getScene().getWindow();
            stage.setScene(new javafx.scene.Scene(root));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
