package controller;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.stage.Stage;
import javafx.event.ActionEvent;
import javafx.scene.control.Alert.AlertType;
import javafx.application.Platform;
import javafx.util.StringConverter;

import dao.HistoriqueVehiculeDAO;
import dao.VehiculeDAO;
import model.HistoriqueVehicule;
import model.Vehicule;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Contrôleur pour l'historique des véhicules
 * Traçabilité complète des événements
 */
public class HistoriqueVehiculeController {

    @FXML private TableView<HistoriqueVehicule> historiqueTable;
    @FXML private TableColumn<HistoriqueVehicule, Long> idColumn;
    @FXML private TableColumn<HistoriqueVehicule, String> vehiculeColumn;
    @FXML private TableColumn<HistoriqueVehicule, String> typeColumn;
    @FXML private TableColumn<HistoriqueVehicule, String> dateColumn;
    @FXML private TableColumn<HistoriqueVehicule, String> descriptionColumn;
    @FXML private TableColumn<HistoriqueVehicule, String> clientColumn;
    @FXML private TableColumn<HistoriqueVehicule, Integer> kilometrageColumn;
    @FXML private TableColumn<HistoriqueVehicule, Double> coutColumn;
    @FXML private TableColumn<HistoriqueVehicule, String> fournisseurColumn;

    @FXML private TextField searchField;
    @FXML private ComboBox<Vehicule> vehiculeFilter;
    @FXML private ComboBox<HistoriqueVehicule.TypeEvenement> typeEvenementFilter;
    @FXML private DatePicker dateDebutFilter;
    @FXML private DatePicker dateFinFilter;
    @FXML private CheckBox evenementsFinanciersCheck;

    @FXML private Label lblTotalEvenements;
    @FXML private Label lblVehiculesConcernes;
    @FXML private Label lblCoutTotal;
    @FXML private Label lblAcquisitions;
    @FXML private Label lblLocations;
    @FXML private Label lblMaintenances;
    @FXML private Label lblAccidents;

    // Panneau de détails
    @FXML private Label detailIdLabel;
    @FXML private Label detailVehiculeLabel;
    @FXML private Label detailTypeLabel;
    @FXML private Label detailDateLabel;
    @FXML private Label detailAgentLabel;
    @FXML private Label detailDescriptionLabel;
    @FXML private Label detailDetailsLabel;
    @FXML private Label detailKmAvantLabel;
    @FXML private Label detailKmApresLabel;
    @FXML private Label detailKmParcouruLabel;
    @FXML private Label detailCoutLabel;
    @FXML private Label detailFournisseurLabel;
    @FXML private Label detailFactureLabel;
    @FXML private Label detailRapportLabel;
    @FXML private Label detailClientLabel;
    @FXML private Label detailLocationLabel;
    @FXML private TextArea detailObservationsArea;

    private HistoriqueVehiculeDAO historiqueDAO;
    private VehiculeDAO vehiculeDAO;
    private ObservableList<HistoriqueVehicule> historiqueList;
    private ObservableList<Vehicule> vehiculeList;

    @FXML
    public void initialize() {
        historiqueDAO = new HistoriqueVehiculeDAO();
        vehiculeDAO = new VehiculeDAO();

        setupTableColumns();
        setupFilters();
        loadData();
        updateStatistics();
        
        // Listener pour afficher les détails
        historiqueTable.getSelectionModel().selectedItemProperty().addListener(
            (obs, oldSelection, newSelection) -> {
                if (newSelection != null) {
                    afficherDetails(newSelection);
                } else {
                    clearDetails();
                }
            }
        );
    }

    private void setupTableColumns() {
        idColumn.setCellValueFactory(new PropertyValueFactory<>("id"));
        
        vehiculeColumn.setCellValueFactory(data -> {
            Vehicule v = data.getValue().getVehicule();
            return new javafx.beans.property.SimpleStringProperty(
                v != null ? v.getImmatriculation() : "");
        });
        
        typeColumn.setCellValueFactory(data -> {
            HistoriqueVehicule.TypeEvenement type = data.getValue().getTypeEvenement();
            return new javafx.beans.property.SimpleStringProperty(
                type != null ? getTypeDisplayName(type) : "");
        });
        
        dateColumn.setCellValueFactory(data -> {
            if (data.getValue().getDateEvenement() != null) {
                return new javafx.beans.property.SimpleStringProperty(
                    data.getValue().getDateEvenement().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
            }
            return new javafx.beans.property.SimpleStringProperty("");
        });
        
        descriptionColumn.setCellValueFactory(new PropertyValueFactory<>("description"));
        
        clientColumn.setCellValueFactory(data -> {
            if (data.getValue().getClient() != null) {
                return new javafx.beans.property.SimpleStringProperty(
                    data.getValue().getClient().getNom() + " " + data.getValue().getClient().getPrenom());
            }
            return new javafx.beans.property.SimpleStringProperty("");
        });
        
        kilometrageColumn.setCellValueFactory(new PropertyValueFactory<>("kilometrageParcouru"));
        
        coutColumn.setCellValueFactory(new PropertyValueFactory<>("coutEvenement"));
        coutColumn.setCellFactory(col -> new TableCell<HistoriqueVehicule, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(String.format("%.2f DH", item));
                }
            }
        });
        
        fournisseurColumn.setCellValueFactory(new PropertyValueFactory<>("fournisseur"));

        // Style des cellules selon le type d'événement
        historiqueTable.setRowFactory(tv -> {
            TableRow<HistoriqueVehicule> row = new TableRow<>();
            row.itemProperty().addListener((obs, oldItem, newItem) -> {
                if (newItem == null) {
                    row.setStyle("");
                } else {
                    switch (newItem.getTypeEvenement()) {
                        case ACQUISITION:
                            row.setStyle("-fx-background-color: #e8f5e8;");
                            break;
                        case ACCIDENT:
                        case SINISTRE:
                        case VOL:
                            row.setStyle("-fx-background-color: #ffe8e8;");
                            break;
                        case MAINTENANCE:
                        case REPARATION:
                            row.setStyle("-fx-background-color: #fff3cd;");
                            break;
                        case LOCATION_DEBUT:
                        case LOCATION_FIN:
                            row.setStyle("-fx-background-color: #e3f2fd;");
                            break;
                        default:
                            row.setStyle("");
                    }
                }
            });
            return row;
        });
    }

    private void setupFilters() {
        // Configuration du filtre véhicule
        vehiculeList = FXCollections.observableArrayList();
        vehiculeFilter.setItems(vehiculeList);
        vehiculeFilter.setConverter(new StringConverter<Vehicule>() {
            @Override
            public String toString(Vehicule vehicule) {
                if (vehicule == null) return "";
                return String.format("%s %s (%s)", 
                    vehicule.getMarque(), vehicule.getModele(), vehicule.getImmatriculation());
            }
            @Override
            public Vehicule fromString(String string) { return null; }
        });

        // Configuration du filtre type
        typeEvenementFilter.setItems(FXCollections.observableArrayList(HistoriqueVehicule.TypeEvenement.values()));
        typeEvenementFilter.setConverter(new StringConverter<HistoriqueVehicule.TypeEvenement>() {
            @Override
            public String toString(HistoriqueVehicule.TypeEvenement type) {
                return type != null ? getTypeDisplayName(type) : "";
            }
            @Override
            public HistoriqueVehicule.TypeEvenement fromString(String string) { return null; }
        });

        // Listeners pour les filtres
        searchField.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        vehiculeFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        typeEvenementFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        dateDebutFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        dateFinFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        evenementsFinanciersCheck.selectedProperty().addListener((obs, oldVal, newVal) -> applyFilters());
    }

    private void loadData() {
        try {
            // Charger l'historique
            List<HistoriqueVehicule> historique = historiqueDAO.findAll();
            historiqueList = FXCollections.observableArrayList(historique);
            historiqueTable.setItems(historiqueList);

            // Charger les véhicules
            List<Vehicule> vehicules = vehiculeDAO.findAll();
            vehiculeList.setAll(vehicules);

        } catch (Exception e) {
            showAlert("Erreur lors du chargement des données: " + e.getMessage(), AlertType.ERROR);
            e.printStackTrace();
        }
    }

    private void applyFilters() {
        if (historiqueList == null) return;

        ObservableList<HistoriqueVehicule> filteredList = FXCollections.observableArrayList();
        String searchText = searchField.getText().toLowerCase();
        Vehicule selectedVehicule = vehiculeFilter.getValue();
        HistoriqueVehicule.TypeEvenement selectedType = typeEvenementFilter.getValue();
        LocalDate dateDebut = dateDebutFilter.getValue();
        LocalDate dateFin = dateFinFilter.getValue();
        boolean seulementsFinanciers = evenementsFinanciersCheck.isSelected();

        for (HistoriqueVehicule historique : historiqueList) {
            boolean matches = true;

            // Filtre par texte de recherche
            if (!searchText.isEmpty()) {
                String searchableText = "";
                if (historique.getVehicule() != null) {
                    searchableText += historique.getVehicule().getImmatriculation() + " " +
                                    historique.getVehicule().getMarque() + " " +
                                    historique.getVehicule().getModele() + " ";
                }
                searchableText += historique.getDescription() != null ? historique.getDescription() : "";
                searchableText += historique.getFournisseur() != null ? historique.getFournisseur() : "";
                
                if (!searchableText.toLowerCase().contains(searchText)) {
                    matches = false;
                }
            }

            // Filtre par véhicule
            if (selectedVehicule != null && !historique.getVehicule().equals(selectedVehicule)) {
                matches = false;
            }

            // Filtre par type
            if (selectedType != null && historique.getTypeEvenement() != selectedType) {
                matches = false;
            }

            // Filtre par période
            if (dateDebut != null && historique.getDateEvenement() != null &&
                historique.getDateEvenement().toLocalDate().isBefore(dateDebut)) {
                matches = false;
            }
            if (dateFin != null && historique.getDateEvenement() != null &&
                historique.getDateEvenement().toLocalDate().isAfter(dateFin)) {
                matches = false;
            }

            // Filtre événements financiers
            if (seulementsFinanciers && (historique.getCoutEvenement() == null || historique.getCoutEvenement() <= 0)) {
                matches = false;
            }

            if (matches) {
                filteredList.add(historique);
            }
        }

        historiqueTable.setItems(filteredList);
        lblTotalEvenements.setText("Événements: " + filteredList.size());
    }

    private void afficherDetails(HistoriqueVehicule historique) {
        detailIdLabel.setText(String.valueOf(historique.getId()));
        
        if (historique.getVehicule() != null) {
            detailVehiculeLabel.setText(String.format("%s %s (%s)", 
                historique.getVehicule().getMarque(), 
                historique.getVehicule().getModele(), 
                historique.getVehicule().getImmatriculation()));
        } else {
            detailVehiculeLabel.setText("");
        }
        
        detailTypeLabel.setText(getTypeDisplayName(historique.getTypeEvenement()));
        
        if (historique.getDateEvenement() != null) {
            detailDateLabel.setText(historique.getDateEvenement().format(
                DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")));
        } else {
            detailDateLabel.setText("");
        }
        
        detailAgentLabel.setText(historique.getCreatedBy() != null ? historique.getCreatedBy() : "");
        detailDescriptionLabel.setText(historique.getDescription() != null ? historique.getDescription() : "");
        detailDetailsLabel.setText(historique.getDetailsEvenement() != null ? historique.getDetailsEvenement() : "");
        
        // Kilométrage
        detailKmAvantLabel.setText(historique.getKilometrageAvant() != null ? 
            String.valueOf(historique.getKilometrageAvant()) + " km" : "");
        detailKmApresLabel.setText(historique.getKilometrageApres() != null ? 
            String.valueOf(historique.getKilometrageApres()) + " km" : "");
        detailKmParcouruLabel.setText(historique.getKilometrageParcouru() != null ? 
            String.valueOf(historique.getKilometrageParcouru()) + " km" : "");
        
        // Informations financières
        detailCoutLabel.setText(historique.getCoutEvenement() != null ? 
            String.format("%.2f DH", historique.getCoutEvenement()) : "");
        detailFournisseurLabel.setText(historique.getFournisseur() != null ? historique.getFournisseur() : "");
        detailFactureLabel.setText(historique.getNumeroFacture() != null ? historique.getNumeroFacture() : "");
        detailRapportLabel.setText(historique.getNumeroRapport() != null ? historique.getNumeroRapport() : "");
        
        // Client et location
        if (historique.getClient() != null) {
            detailClientLabel.setText(historique.getClient().getNom() + " " + historique.getClient().getPrenom());
        } else {
            detailClientLabel.setText("");
        }
        
        if (historique.getLocation() != null) {
            detailLocationLabel.setText("LOC-" + historique.getLocation().getId());
        } else {
            detailLocationLabel.setText("");
        }
        
        // Observations
        String observations = "";
        if (historique.getDetailsEvenement() != null) {
            observations += historique.getDetailsEvenement();
        }
        if (historique.getNumeroPoliceAssurance() != null) {
            observations += "\nN° Police Assurance: " + historique.getNumeroPoliceAssurance();
        }
        detailObservationsArea.setText(observations);
    }

    private void clearDetails() {
        detailIdLabel.setText("");
        detailVehiculeLabel.setText("");
        detailTypeLabel.setText("");
        detailDateLabel.setText("");
        detailAgentLabel.setText("");
        detailDescriptionLabel.setText("");
        detailDetailsLabel.setText("");
        detailKmAvantLabel.setText("");
        detailKmApresLabel.setText("");
        detailKmParcouruLabel.setText("");
        detailCoutLabel.setText("");
        detailFournisseurLabel.setText("");
        detailFactureLabel.setText("");
        detailRapportLabel.setText("");
        detailClientLabel.setText("");
        detailLocationLabel.setText("");
        detailObservationsArea.setText("");
    }

    private void updateStatistics() {
        try {
            Object[] stats = historiqueDAO.getStatistiquesHistorique();
            if (stats != null && stats.length >= 6) {
                long totalEvenements = ((Number) stats[0]).longValue();
                long vehiculesConcernes = ((Number) stats[1]).longValue();
                double coutTotal = stats[2] != null ? ((Number) stats[2]).doubleValue() : 0.0;
                long nombreAccidents = ((Number) stats[4]).longValue();
                long nombreMaintenances = ((Number) stats[5]).longValue();

                Platform.runLater(() -> {
                    lblTotalEvenements.setText("Événements: " + totalEvenements);
                    lblVehiculesConcernes.setText("Véhicules: " + vehiculesConcernes);
                    lblCoutTotal.setText(String.format("Coût total: %.2f DH", coutTotal));
                    lblAccidents.setText("Accidents: " + nombreAccidents);
                    lblMaintenances.setText("Maintenances: " + nombreMaintenances);
                });
            }

            // Statistiques par type
            List<Object[]> statsParType = historiqueDAO.compterEvenementsParType();
            long acquisitions = 0, locations = 0;
            
            for (Object[] stat : statsParType) {
                HistoriqueVehicule.TypeEvenement type = (HistoriqueVehicule.TypeEvenement) stat[0];
                long count = ((Number) stat[1]).longValue();
                
                switch (type) {
                    case ACQUISITION:
                        acquisitions = count;
                        break;
                    case LOCATION_DEBUT:
                    case LOCATION_FIN:
                        locations += count;
                        break;
                }
            }
            
            final long finalAcquisitions = acquisitions;
            final long finalLocations = locations;
            
            Platform.runLater(() -> {
                lblAcquisitions.setText("Acquisitions: " + finalAcquisitions);
                lblLocations.setText("Locations: " + finalLocations);
            });

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @FXML
    private void handleExporterHistorique() {
        // TODO: Implémenter l'export vers Excel/PDF
        showAlert("Fonctionnalité d'export en cours de développement.", AlertType.INFORMATION);
    }

    private String getTypeDisplayName(HistoriqueVehicule.TypeEvenement type) {
        switch (type) {
            case ACQUISITION: return "Acquisition";
            case LOCATION_DEBUT: return "Début location";
            case LOCATION_FIN: return "Fin location";
            case MAINTENANCE: return "Maintenance";
            case ACCIDENT: return "Accident";
            case CONTROLE_TECHNIQUE: return "Contrôle technique";
            case REPARATION: return "Réparation";
            case CHANGEMENT_ETAT: return "Changement d'état";
            case MISE_HORS_SERVICE: return "Mise hors service";
            case VENTE: return "Vente";
            case VOL: return "Vol";
            case SINISTRE: return "Sinistre";
            default: return type.toString();
        }
    }

    private void showAlert(String message, AlertType type) {
        Alert alert = new Alert(type);
        alert.setTitle("Historique des Véhicules");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    @FXML
    private void navigateToPage(ActionEvent event) {
        NavigationController.handleNavigation(event, (Stage) historiqueTable.getScene().getWindow());
    }

    @FXML
    private void handleLogout(ActionEvent event) {
        try {
            LoginController.loggedInUser = null;
            javafx.scene.Parent root = javafx.fxml.FXMLLoader.load(getClass().getResource("/view/login.fxml"));
            Stage stage = (Stage) historiqueTable.getScene().getWindow();
            stage.setScene(new javafx.scene.Scene(root));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
