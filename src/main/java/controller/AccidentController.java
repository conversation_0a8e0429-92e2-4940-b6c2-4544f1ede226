package controller;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.stage.Stage;
import javafx.event.ActionEvent;
import javafx.scene.control.Alert.AlertType;
import javafx.application.Platform;
import javafx.util.StringConverter;

import dao.AccidentDAO;
import dao.VehiculeDAO;
import dao.LocationDAO;
import model.Accident;
import model.Vehicule;
import model.Location;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

/**
 * Contrôleur pour la gestion des accidents
 * Conforme à la réglementation marocaine des assurances
 */
public class AccidentController {

    @FXML private TableView<Accident> accidentTable;
    @FXML private TableColumn<Accident, Long> idColumn;
    @FXML private TableColumn<Accident, String> vehiculeColumn;
    @FXML private TableColumn<Accident, String> clientColumn;
    @FXML private TableColumn<Accident, String> dateColumn;
    @FXML private TableColumn<Accident, String> lieuColumn;
    @FXML private TableColumn<Accident, String> typeColumn;
    @FXML private TableColumn<Accident, String> graviteColumn;
    @FXML private TableColumn<Accident, String> statutColumn;
    @FXML private TableColumn<Accident, Double> degatsColumn;

    @FXML private TextField searchField;
    @FXML private ComboBox<Accident.TypeAccident> typeFilter;
    @FXML private ComboBox<Accident.GraviteAccident> graviteFilter;
    @FXML private ComboBox<Accident.StatutAccident> statutFilter;
    @FXML private DatePicker dateDebutFilter;
    @FXML private DatePicker dateFinFilter;

    @FXML private Label lblTotalCount;
    @FXML private Label lblAccidentsGraves;
    @FXML private Label lblEnCours;
    @FXML private Label lblResolus;

    // Formulaire d'accident
    @FXML private ComboBox<Vehicule> vehiculeComboBox;
    @FXML private ComboBox<Location> locationComboBox;
    @FXML private DatePicker dateAccidentPicker;
    @FXML private TextField heureField;
    @FXML private TextField lieuField;
    @FXML private TextField villeField;
    @FXML private ComboBox<Accident.TypeAccident> typeAccidentComboBox;
    @FXML private ComboBox<Accident.GraviteAccident> graviteComboBox;
    @FXML private TextArea descriptionArea;

    // Circonstances
    @FXML private TextArea circonstancesArea;
    @FXML private TextField meteoField;
    @FXML private TextField chausseeField;
    @FXML private TextField visibiliteField;
    @FXML private ComboBox<Accident.Responsabilite> responsabiliteComboBox;
    @FXML private TextArea detailsResponsabiliteArea;

    // Dégâts et blessés
    @FXML private CheckBox blessesClientCheck;
    @FXML private CheckBox blessesTiersCheck;
    @FXML private TextField blessesLegersField;
    @FXML private TextField blessesGravesField;
    @FXML private TextField decesField;
    @FXML private TextArea degatsVehiculeArea;
    @FXML private TextField estimationField;
    @FXML private CheckBox autresVehiculesCheck;
    @FXML private TextField nombreVehiculesField;

    // Assurance
    @FXML private TextField constatField;
    @FXML private TextField declarationPoliceField;
    @FXML private TextField commissariatField;
    @FXML private TextField compagnieField;
    @FXML private TextField policeField;
    @FXML private TextField sinistreField;
    @FXML private TextField indemnisationField;
    @FXML private CheckBox priseEnChargeCheck;
    @FXML private CheckBox reparableCheck;

    private AccidentDAO accidentDAO;
    private VehiculeDAO vehiculeDAO;
    private LocationDAO locationDAO;
    private ObservableList<Accident> accidentList;
    private ObservableList<Vehicule> vehiculeList;
    private ObservableList<Location> locationList;

    private boolean isEditMode = false;
    private Accident selectedAccident = null;

    @FXML
    public void initialize() {
        accidentDAO = new AccidentDAO();
        vehiculeDAO = new VehiculeDAO();
        locationDAO = new LocationDAO();

        setupTableColumns();
        setupFilters();
        setupFormControls();
        loadData();
        updateStatistics();
    }

    private void setupTableColumns() {
        idColumn.setCellValueFactory(new PropertyValueFactory<>("id"));
        
        vehiculeColumn.setCellValueFactory(data -> {
            Vehicule v = data.getValue().getVehicule();
            return new javafx.beans.property.SimpleStringProperty(
                v != null ? v.getImmatriculation() : "");
        });
        
        clientColumn.setCellValueFactory(data -> {
            if (data.getValue().getClient() != null) {
                return new javafx.beans.property.SimpleStringProperty(
                    data.getValue().getClient().getNom() + " " + data.getValue().getClient().getPrenom());
            }
            return new javafx.beans.property.SimpleStringProperty("");
        });
        
        dateColumn.setCellValueFactory(data -> {
            LocalDate date = data.getValue().getDateAccident();
            return new javafx.beans.property.SimpleStringProperty(
                date != null ? date.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "");
        });
        
        lieuColumn.setCellValueFactory(data -> {
            String lieu = data.getValue().getLieuAccident();
            return new javafx.beans.property.SimpleStringProperty(lieu != null ? lieu : "");
        });
        
        typeColumn.setCellValueFactory(data -> {
            Accident.TypeAccident type = data.getValue().getTypeAccident();
            return new javafx.beans.property.SimpleStringProperty(
                type != null ? getTypeDisplayName(type) : "");
        });
        
        graviteColumn.setCellValueFactory(data -> {
            Accident.GraviteAccident gravite = data.getValue().getGravite();
            return new javafx.beans.property.SimpleStringProperty(
                gravite != null ? getGraviteDisplayName(gravite) : "");
        });
        
        statutColumn.setCellValueFactory(data -> {
            Accident.StatutAccident statut = data.getValue().getStatut();
            return new javafx.beans.property.SimpleStringProperty(
                statut != null ? getStatutDisplayName(statut) : "");
        });
        
        degatsColumn.setCellValueFactory(new PropertyValueFactory<>("estimationDegats"));
        degatsColumn.setCellFactory(col -> new TableCell<Accident, Double>() {
            @Override
            protected void updateItem(Double item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(String.format("%.0f DH", item));
                }
            }
        });

        // Style des cellules selon la gravité
        accidentTable.setRowFactory(tv -> {
            TableRow<Accident> row = new TableRow<>();
            row.itemProperty().addListener((obs, oldItem, newItem) -> {
                if (newItem == null) {
                    row.setStyle("");
                } else {
                    switch (newItem.getGravite()) {
                        case MORTEL:
                            row.setStyle("-fx-background-color: #8b0000; -fx-text-fill: white;");
                            break;
                        case TRES_GRAVE:
                            row.setStyle("-fx-background-color: #dc143c; -fx-text-fill: white;");
                            break;
                        case GRAVE:
                            row.setStyle("-fx-background-color: #ff6347;");
                            break;
                        case MOYEN:
                            row.setStyle("-fx-background-color: #ffa500;");
                            break;
                        case LEGER:
                            row.setStyle("-fx-background-color: #ffffe0;");
                            break;
                        default:
                            row.setStyle("");
                    }
                }
            });
            return row;
        });
    }

    private void setupFilters() {
        // Configuration des ComboBox de filtres
        typeFilter.setItems(FXCollections.observableArrayList(Accident.TypeAccident.values()));
        typeFilter.setConverter(new StringConverter<Accident.TypeAccident>() {
            @Override
            public String toString(Accident.TypeAccident type) {
                return type != null ? getTypeDisplayName(type) : "";
            }
            @Override
            public Accident.TypeAccident fromString(String string) { return null; }
        });

        graviteFilter.setItems(FXCollections.observableArrayList(Accident.GraviteAccident.values()));
        graviteFilter.setConverter(new StringConverter<Accident.GraviteAccident>() {
            @Override
            public String toString(Accident.GraviteAccident gravite) {
                return gravite != null ? getGraviteDisplayName(gravite) : "";
            }
            @Override
            public Accident.GraviteAccident fromString(String string) { return null; }
        });

        statutFilter.setItems(FXCollections.observableArrayList(Accident.StatutAccident.values()));
        statutFilter.setConverter(new StringConverter<Accident.StatutAccident>() {
            @Override
            public String toString(Accident.StatutAccident statut) {
                return statut != null ? getStatutDisplayName(statut) : "";
            }
            @Override
            public Accident.StatutAccident fromString(String string) { return null; }
        });

        // Listeners pour les filtres
        searchField.textProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        typeFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        graviteFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        statutFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        dateDebutFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
        dateFinFilter.valueProperty().addListener((obs, oldVal, newVal) -> applyFilters());
    }

    private void setupFormControls() {
        // Configuration des ComboBox du formulaire
        vehiculeList = FXCollections.observableArrayList();
        vehiculeComboBox.setItems(vehiculeList);
        vehiculeComboBox.setConverter(new StringConverter<Vehicule>() {
            @Override
            public String toString(Vehicule vehicule) {
                if (vehicule == null) return "";
                return String.format("%s %s (%s)", 
                    vehicule.getMarque(), vehicule.getModele(), vehicule.getImmatriculation());
            }
            @Override
            public Vehicule fromString(String string) { return null; }
        });

        locationList = FXCollections.observableArrayList();
        locationComboBox.setItems(locationList);
        locationComboBox.setConverter(new StringConverter<Location>() {
            @Override
            public String toString(Location location) {
                if (location == null) return "";
                String client = location.getClient() != null ? 
                    location.getClient().getNom() + " " + location.getClient().getPrenom() : "Client inconnu";
                return String.format("LOC-%d - %s", location.getId(), client);
            }
            @Override
            public Location fromString(String string) { return null; }
        });

        // Configuration des autres ComboBox
        typeAccidentComboBox.setItems(FXCollections.observableArrayList(Accident.TypeAccident.values()));
        typeAccidentComboBox.setConverter(new StringConverter<Accident.TypeAccident>() {
            @Override
            public String toString(Accident.TypeAccident type) {
                return type != null ? getTypeDisplayName(type) : "";
            }
            @Override
            public Accident.TypeAccident fromString(String string) { return null; }
        });

        graviteComboBox.setItems(FXCollections.observableArrayList(Accident.GraviteAccident.values()));
        graviteComboBox.setConverter(new StringConverter<Accident.GraviteAccident>() {
            @Override
            public String toString(Accident.GraviteAccident gravite) {
                return gravite != null ? getGraviteDisplayName(gravite) : "";
            }
            @Override
            public Accident.GraviteAccident fromString(String string) { return null; }
        });

        responsabiliteComboBox.setItems(FXCollections.observableArrayList(Accident.Responsabilite.values()));
        responsabiliteComboBox.setConverter(new StringConverter<Accident.Responsabilite>() {
            @Override
            public String toString(Accident.Responsabilite resp) {
                return resp != null ? getResponsabiliteDisplayName(resp) : "";
            }
            @Override
            public Accident.Responsabilite fromString(String string) { return null; }
        });

        // Validation des champs numériques
        setupNumericField(blessesLegersField);
        setupNumericField(blessesGravesField);
        setupNumericField(decesField);
        setupNumericField(nombreVehiculesField);
        setupDecimalField(estimationField);
        setupDecimalField(indemnisationField);

        // Mise à jour automatique du client quand on sélectionne une location
        locationComboBox.valueProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal != null) {
                vehiculeComboBox.setValue(newVal.getVehicule());
            }
        });
    }

    private void setupNumericField(TextField field) {
        field.textProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal.matches("\\d*")) {
                field.setText(oldVal);
            }
        });
    }

    private void setupDecimalField(TextField field) {
        field.textProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal.matches("\\d*(\\.\\d*)?")) {
                field.setText(oldVal);
            }
        });
    }

    private void loadData() {
        try {
            // Charger les accidents
            List<Accident> accidents = accidentDAO.findAll();
            accidentList = FXCollections.observableArrayList(accidents);
            accidentTable.setItems(accidentList);

            // Charger les véhicules et locations
            List<Vehicule> vehicules = vehiculeDAO.findAll();
            vehiculeList.setAll(vehicules);

            List<Location> locations = locationDAO.findAll();
            locationList.setAll(locations);

        } catch (Exception e) {
            showAlert("Erreur lors du chargement des données: " + e.getMessage(), AlertType.ERROR);
            e.printStackTrace();
        }
    }

    private void applyFilters() {
        if (accidentList == null) return;

        ObservableList<Accident> filteredList = FXCollections.observableArrayList();
        String searchText = searchField.getText().toLowerCase();
        Accident.TypeAccident selectedType = typeFilter.getValue();
        Accident.GraviteAccident selectedGravite = graviteFilter.getValue();
        Accident.StatutAccident selectedStatut = statutFilter.getValue();
        LocalDate dateDebut = dateDebutFilter.getValue();
        LocalDate dateFin = dateFinFilter.getValue();

        for (Accident accident : accidentList) {
            boolean matches = true;

            // Filtre par texte de recherche
            if (!searchText.isEmpty()) {
                String searchableText = "";
                if (accident.getVehicule() != null) {
                    searchableText += accident.getVehicule().getImmatriculation() + " ";
                }
                if (accident.getClient() != null) {
                    searchableText += accident.getClient().getNom() + " " + accident.getClient().getPrenom() + " ";
                }
                searchableText += accident.getLieuAccident() != null ? accident.getLieuAccident() : "";
                searchableText += accident.getVilleAccident() != null ? accident.getVilleAccident() : "";
                
                if (!searchableText.toLowerCase().contains(searchText)) {
                    matches = false;
                }
            }

            // Filtres par énumérations
            if (selectedType != null && accident.getTypeAccident() != selectedType) {
                matches = false;
            }
            if (selectedGravite != null && accident.getGravite() != selectedGravite) {
                matches = false;
            }
            if (selectedStatut != null && accident.getStatut() != selectedStatut) {
                matches = false;
            }

            // Filtre par période
            if (dateDebut != null && accident.getDateAccident() != null &&
                accident.getDateAccident().isBefore(dateDebut)) {
                matches = false;
            }
            if (dateFin != null && accident.getDateAccident() != null &&
                accident.getDateAccident().isAfter(dateFin)) {
                matches = false;
            }

            if (matches) {
                filteredList.add(accident);
            }
        }

        accidentTable.setItems(filteredList);
        lblTotalCount.setText("Total: " + filteredList.size());
    }

    @FXML
    private void handleNouvelAccident() {
        clearForm();
        isEditMode = false;
        selectedAccident = null;
        dateAccidentPicker.setValue(LocalDate.now());
        heureField.setText(LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm")));
    }

    @FXML
    private void handleModifierAccident() {
        Accident selected = accidentTable.getSelectionModel().getSelectedItem();
        if (selected == null) {
            showAlert("Veuillez sélectionner un accident à modifier.", AlertType.WARNING);
            return;
        }

        loadAccidentInForm(selected);
        isEditMode = true;
        selectedAccident = selected;
    }

    @FXML
    private void handleSauvegarderAccident() {
        if (!validateForm()) return;

        try {
            Accident accident = isEditMode ? selectedAccident : new Accident();
            
            // Informations de base
            accident.setVehicule(vehiculeComboBox.getValue());
            accident.setLocation(locationComboBox.getValue());
            if (locationComboBox.getValue() != null) {
                accident.setClient(locationComboBox.getValue().getClient());
            }
            accident.setDateAccident(dateAccidentPicker.getValue());
            
            if (!heureField.getText().isEmpty()) {
                try {
                    accident.setHeureAccident(LocalTime.parse(heureField.getText()));
                } catch (Exception e) {
                    // Ignore invalid time format
                }
            }
            
            accident.setLieuAccident(lieuField.getText());
            accident.setVilleAccident(villeField.getText());
            accident.setTypeAccident(typeAccidentComboBox.getValue());
            accident.setGravite(graviteComboBox.getValue());
            accident.setDescriptionAccident(descriptionArea.getText());

            // Circonstances
            accident.setCirconstancesAccident(circonstancesArea.getText());
            accident.setConditionsMeteo(meteoField.getText());
            accident.setEtatChaussee(chausseeField.getText());
            accident.setVisibilite(visibiliteField.getText());
            accident.setResponsabilite(responsabiliteComboBox.getValue());
            accident.setDetailsResponsabilite(detailsResponsabiliteArea.getText());

            // Dégâts et blessés
            accident.setBlessesClient(blessesClientCheck.isSelected());
            accident.setBlessesTiers(blessesTiersCheck.isSelected());
            
            if (!blessesLegersField.getText().isEmpty()) {
                accident.setNombreBlessesLegers(Integer.parseInt(blessesLegersField.getText()));
            }
            if (!blessesGravesField.getText().isEmpty()) {
                accident.setNombreBlessesGraves(Integer.parseInt(blessesGravesField.getText()));
            }
            if (!decesField.getText().isEmpty()) {
                accident.setNombreDeces(Integer.parseInt(decesField.getText()));
            }
            
            accident.setDegatsVehiculeLoue(degatsVehiculeArea.getText());
            
            if (!estimationField.getText().isEmpty()) {
                accident.setEstimationDegats(Double.parseDouble(estimationField.getText()));
            }
            
            accident.setAutresVehiculesImpliques(autresVehiculesCheck.isSelected());
            if (!nombreVehiculesField.getText().isEmpty()) {
                accident.setNombreVehiculesImpliques(Integer.parseInt(nombreVehiculesField.getText()));
            }

            // Assurance
            accident.setNumeroConstatAmiable(constatField.getText());
            accident.setNumeroDeclarationPolice(declarationPoliceField.getText());
            accident.setCommissariatCompetent(commissariatField.getText());
            accident.setCompagnieAssurance(compagnieField.getText());
            accident.setNumeroPoliceAssurance(policeField.getText());
            accident.setNumeroSinistre(sinistreField.getText());
            
            if (!indemnisationField.getText().isEmpty()) {
                accident.setMontantIndemnisation(Double.parseDouble(indemnisationField.getText()));
            }
            
            accident.setPriseEnChargeAssurance(priseEnChargeCheck.isSelected());
            accident.setVehiculeReparable(reparableCheck.isSelected());

            if (!isEditMode) {
                String agentNom = LoginController.loggedInUser != null ? 
                    LoginController.loggedInUser.getUsername() : "Agent";
                accident.setAgentDeclarant(agentNom);
            }

            accidentDAO.save(accident);
            
            showAlert(isEditMode ? "Accident modifié avec succès!" : "Accident déclaré avec succès!", 
                     AlertType.INFORMATION);
            
            clearForm();
            loadData();
            updateStatistics();
            
        } catch (Exception e) {
            showAlert("Erreur lors de la sauvegarde: " + e.getMessage(), AlertType.ERROR);
            e.printStackTrace();
        }
    }

    @FXML
    private void handleVoirDetails() {
        Accident selected = accidentTable.getSelectionModel().getSelectedItem();
        if (selected == null) {
            showAlert("Veuillez sélectionner un accident.", AlertType.WARNING);
            return;
        }

        // Afficher les détails dans une nouvelle fenêtre ou dialog
        Alert alert = new Alert(AlertType.INFORMATION);
        alert.setTitle("Détails de l'accident");
        alert.setHeaderText("Accident ID: " + selected.getId());
        
        StringBuilder details = new StringBuilder();
        details.append("Véhicule: ").append(selected.getVehicule().getImmatriculation()).append("\n");
        details.append("Date: ").append(selected.getDateAccident()).append("\n");
        details.append("Lieu: ").append(selected.getLieuAccident()).append("\n");
        details.append("Type: ").append(getTypeDisplayName(selected.getTypeAccident())).append("\n");
        details.append("Gravité: ").append(getGraviteDisplayName(selected.getGravite())).append("\n");
        details.append("Statut: ").append(getStatutDisplayName(selected.getStatut())).append("\n");
        if (selected.getEstimationDegats() != null) {
            details.append("Estimation dégâts: ").append(String.format("%.2f DH", selected.getEstimationDegats())).append("\n");
        }
        
        alert.setContentText(details.toString());
        alert.showAndWait();
    }

    @FXML
    private void handleDeclareAssurance() {
        Accident selected = accidentTable.getSelectionModel().getSelectedItem();
        if (selected == null) {
            showAlert("Veuillez sélectionner un accident.", AlertType.WARNING);
            return;
        }

        // Dialog pour saisir les informations d'assurance
        TextInputDialog dialog = new TextInputDialog();
        dialog.setTitle("Déclaration Assurance");
        dialog.setHeaderText("Numéro de sinistre");
        dialog.setContentText("Veuillez saisir le numéro de sinistre:");

        Optional<String> result = dialog.showAndWait();
        if (result.isPresent()) {
            try {
                selected.declareALAssurance("DECL-" + System.currentTimeMillis(), result.get());
                accidentDAO.save(selected);
                
                showAlert("Déclaration d'assurance enregistrée!", AlertType.INFORMATION);
                loadData();
                
            } catch (Exception e) {
                showAlert("Erreur: " + e.getMessage(), AlertType.ERROR);
            }
        }
    }

    private boolean validateForm() {
        if (vehiculeComboBox.getValue() == null) {
            showAlert("Veuillez sélectionner un véhicule.", AlertType.WARNING);
            return false;
        }
        
        if (dateAccidentPicker.getValue() == null) {
            showAlert("Veuillez sélectionner la date de l'accident.", AlertType.WARNING);
            return false;
        }
        
        if (typeAccidentComboBox.getValue() == null) {
            showAlert("Veuillez sélectionner le type d'accident.", AlertType.WARNING);
            return false;
        }
        
        if (graviteComboBox.getValue() == null) {
            showAlert("Veuillez sélectionner la gravité de l'accident.", AlertType.WARNING);
            return false;
        }
        
        return true;
    }

    private void loadAccidentInForm(Accident accident) {
        vehiculeComboBox.setValue(accident.getVehicule());
        locationComboBox.setValue(accident.getLocation());
        dateAccidentPicker.setValue(accident.getDateAccident());
        
        if (accident.getHeureAccident() != null) {
            heureField.setText(accident.getHeureAccident().format(DateTimeFormatter.ofPattern("HH:mm")));
        }
        
        lieuField.setText(accident.getLieuAccident());
        villeField.setText(accident.getVilleAccident());
        typeAccidentComboBox.setValue(accident.getTypeAccident());
        graviteComboBox.setValue(accident.getGravite());
        descriptionArea.setText(accident.getDescriptionAccident());

        // Circonstances
        circonstancesArea.setText(accident.getCirconstancesAccident());
        meteoField.setText(accident.getConditionsMeteo());
        chausseeField.setText(accident.getEtatChaussee());
        visibiliteField.setText(accident.getVisibilite());
        responsabiliteComboBox.setValue(accident.getResponsabilite());
        detailsResponsabiliteArea.setText(accident.getDetailsResponsabilite());

        // Dégâts et blessés
        blessesClientCheck.setSelected(accident.isBlessesClient());
        blessesTiersCheck.setSelected(accident.isBlessesTiers());
        blessesLegersField.setText(String.valueOf(accident.getNombreBlessesLegers()));
        blessesGravesField.setText(String.valueOf(accident.getNombreBlessesGraves()));
        decesField.setText(String.valueOf(accident.getNombreDeces()));
        degatsVehiculeArea.setText(accident.getDegatsVehiculeLoue());
        
        if (accident.getEstimationDegats() != null) {
            estimationField.setText(String.valueOf(accident.getEstimationDegats()));
        }
        
        autresVehiculesCheck.setSelected(accident.isAutresVehiculesImpliques());
        nombreVehiculesField.setText(String.valueOf(accident.getNombreVehiculesImpliques()));

        // Assurance
        constatField.setText(accident.getNumeroConstatAmiable());
        declarationPoliceField.setText(accident.getNumeroDeclarationPolice());
        commissariatField.setText(accident.getCommissariatCompetent());
        compagnieField.setText(accident.getCompagnieAssurance());
        policeField.setText(accident.getNumeroPoliceAssurance());
        sinistreField.setText(accident.getNumeroSinistre());
        
        if (accident.getMontantIndemnisation() != null) {
            indemnisationField.setText(String.valueOf(accident.getMontantIndemnisation()));
        }
        
        priseEnChargeCheck.setSelected(accident.isPriseEnChargeAssurance());
        reparableCheck.setSelected(accident.isVehiculeReparable());
    }

    private void clearForm() {
        vehiculeComboBox.setValue(null);
        locationComboBox.setValue(null);
        dateAccidentPicker.setValue(null);
        heureField.clear();
        lieuField.clear();
        villeField.clear();
        typeAccidentComboBox.setValue(null);
        graviteComboBox.setValue(null);
        descriptionArea.clear();

        circonstancesArea.clear();
        meteoField.clear();
        chausseeField.clear();
        visibiliteField.clear();
        responsabiliteComboBox.setValue(null);
        detailsResponsabiliteArea.clear();

        blessesClientCheck.setSelected(false);
        blessesTiersCheck.setSelected(false);
        blessesLegersField.clear();
        blessesGravesField.clear();
        decesField.clear();
        degatsVehiculeArea.clear();
        estimationField.clear();
        autresVehiculesCheck.setSelected(false);
        nombreVehiculesField.clear();

        constatField.clear();
        declarationPoliceField.clear();
        commissariatField.clear();
        compagnieField.clear();
        policeField.clear();
        sinistreField.clear();
        indemnisationField.clear();
        priseEnChargeCheck.setSelected(false);
        reparableCheck.setSelected(true);
    }

    private void updateStatistics() {
        try {
            Object[] stats = accidentDAO.getStatistiquesAccidents();
            if (stats != null && stats.length >= 4) {
                long total = ((Number) stats[0]).longValue();
                long graves = ((Number) stats[1]).longValue();
                long enCours = ((Number) stats[3]).longValue();
                long resolus = total - enCours;

                Platform.runLater(() -> {
                    lblTotalCount.setText("Total: " + total);
                    lblAccidentsGraves.setText("Graves: " + graves);
                    lblEnCours.setText("En cours: " + enCours);
                    lblResolus.setText("Résolus: " + resolus);
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // Méthodes utilitaires pour les noms d'affichage
    private String getTypeDisplayName(Accident.TypeAccident type) {
        switch (type) {
            case COLLISION_FRONTALE: return "Collision frontale";
            case COLLISION_ARRIERE: return "Collision arrière";
            case COLLISION_LATERALE: return "Collision latérale";
            case SORTIE_ROUTE: return "Sortie de route";
            case RENVERSEMENT: return "Renversement";
            case CHOC_OBSTACLE: return "Choc obstacle";
            case INCENDIE: return "Incendie";
            case VOL: return "Vol";
            case VANDALISME: return "Vandalisme";
            case CATASTROPHE_NATURELLE: return "Catastrophe naturelle";
            case AUTRE: return "Autre";
            default: return type.toString();
        }
    }

    private String getGraviteDisplayName(Accident.GraviteAccident gravite) {
        switch (gravite) {
            case LEGER: return "Léger";
            case MOYEN: return "Moyen";
            case GRAVE: return "Grave";
            case TRES_GRAVE: return "Très grave";
            case MORTEL: return "Mortel";
            default: return gravite.toString();
        }
    }

    private String getStatutDisplayName(Accident.StatutAccident statut) {
        switch (statut) {
            case DECLARE: return "Déclaré";
            case EN_COURS_ENQUETE: return "Enquête en cours";
            case EXPERTISE_EN_COURS: return "Expertise en cours";
            case RESOLU: return "Résolu";
            case CONTENTIEUX: return "Contentieux";
            case CLOS: return "Clos";
            default: return statut.toString();
        }
    }

    private String getResponsabiliteDisplayName(Accident.Responsabilite resp) {
        switch (resp) {
            case CLIENT_RESPONSABLE: return "Client responsable";
            case TIERS_RESPONSABLE: return "Tiers responsable";
            case RESPONSABILITE_PARTAGEE: return "Responsabilité partagée";
            case FORCE_MAJEURE: return "Force majeure";
            case EN_COURS_DETERMINATION: return "En cours de détermination";
            default: return resp.toString();
        }
    }

    private void showAlert(String message, AlertType type) {
        Alert alert = new Alert(type);
        alert.setTitle("Gestion des Accidents");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    @FXML
    private void navigateToPage(ActionEvent event) {
        NavigationController.handleNavigation(event, (Stage) accidentTable.getScene().getWindow());
    }

    @FXML
    private void handleLogout(ActionEvent event) {
        try {
            LoginController.loggedInUser = null;
            javafx.scene.Parent root = javafx.fxml.FXMLLoader.load(getClass().getResource("/view/login.fxml"));
            Stage stage = (Stage) accidentTable.getScene().getWindow();
            stage.setScene(new javafx.scene.Scene(root));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
