package dao;

import model.Accident;
import model.Vehicule;
import model.Location;
import model.Client;
import org.hibernate.Session;
import org.hibernate.Transaction;
import util.HibernateUtil;
import java.util.List;
import java.time.LocalDate;

/**
 * DAO pour la gestion des accidents de véhicules
 */
public class AccidentDAO {

    public void save(Accident accident) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.merge(accident);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public void delete(Accident accident) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.remove(accident);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public List<Accident> findAll() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Accident order by dateAccident desc", Accident.class).list();
        } finally {
            session.close();
        }
    }

    public Accident findById(Long id) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.get(Accident.class, id);
        } finally {
            session.close();
        }
    }

    /**
     * Trouve tous les accidents pour un véhicule donné
     */
    public List<Accident> findByVehicule(Vehicule vehicule) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Accident where vehicule = :vehicule order by dateAccident desc", Accident.class)
                    .setParameter("vehicule", vehicule)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve tous les accidents pour un client donné
     */
    public List<Accident> findByClient(Client client) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Accident where client = :client order by dateAccident desc", Accident.class)
                    .setParameter("client", client)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve tous les accidents pour une location donnée
     */
    public List<Accident> findByLocation(Location location) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Accident where location = :location order by dateAccident desc", Accident.class)
                    .setParameter("location", location)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les accidents par type
     */
    public List<Accident> findByType(Accident.TypeAccident type) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Accident where typeAccident = :type order by dateAccident desc", Accident.class)
                    .setParameter("type", type)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les accidents par gravité
     */
    public List<Accident> findByGravite(Accident.GraviteAccident gravite) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Accident where gravite = :gravite order by dateAccident desc", Accident.class)
                    .setParameter("gravite", gravite)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les accidents par statut
     */
    public List<Accident> findByStatut(Accident.StatutAccident statut) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Accident where statut = :statut order by dateAccident desc", Accident.class)
                    .setParameter("statut", statut)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les accidents graves (avec blessés ou décès)
     */
    public List<Accident> findAccidentsGraves() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Accident where gravite in ('GRAVE', 'TRES_GRAVE', 'MORTEL') or nombreBlessesLegers > 0 or nombreBlessesGraves > 0 or nombreDeces > 0 order by dateAccident desc", Accident.class)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les accidents par période
     */
    public List<Accident> findByPeriode(LocalDate dateDebut, LocalDate dateFin) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Accident where dateAccident between :debut and :fin order by dateAccident desc", Accident.class)
                    .setParameter("debut", dateDebut)
                    .setParameter("fin", dateFin)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les accidents par ville
     */
    public List<Accident> findByVille(String ville) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Accident where lower(villeAccident) like :ville order by dateAccident desc", Accident.class)
                    .setParameter("ville", "%" + ville.toLowerCase() + "%")
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les accidents avec responsabilité du client
     */
    public List<Accident> findAccidentsResponsabiliteClient() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Accident where responsabilite in ('CLIENT_RESPONSABLE', 'RESPONSABILITE_PARTAGEE') order by dateAccident desc", Accident.class)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les accidents en cours de traitement
     */
    public List<Accident> findAccidentsEnCours() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Accident where statut in ('DECLARE', 'EN_COURS_ENQUETE', 'EXPERTISE_EN_COURS') order by dateAccident", Accident.class)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Calcule le coût total des dégâts pour une période
     */
    public Double calculerCoutTotalDegats(LocalDate dateDebut, LocalDate dateFin) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            Double result = session.createQuery("select sum(a.estimationDegats) from Accident a where a.dateAccident between :debut and :fin", Double.class)
                    .setParameter("debut", dateDebut)
                    .setParameter("fin", dateFin)
                    .uniqueResult();
            return result != null ? result : 0.0;
        } finally {
            session.close();
        }
    }

    /**
     * Calcule le montant total des indemnisations
     */
    public Double calculerMontantTotalIndemnisations(LocalDate dateDebut, LocalDate dateFin) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            Double result = session.createQuery("select sum(a.montantIndemnisation) from Accident a where a.dateAccident between :debut and :fin and a.montantIndemnisation is not null", Double.class)
                    .setParameter("debut", dateDebut)
                    .setParameter("fin", dateFin)
                    .uniqueResult();
            return result != null ? result : 0.0;
        } finally {
            session.close();
        }
    }

    /**
     * Compte les accidents par type pour statistiques
     */
    public List<Object[]> compterAccidentsParType() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("select typeAccident, count(*) from Accident group by typeAccident order by count(*) desc", Object[].class)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Compte les accidents par gravité
     */
    public List<Object[]> compterAccidentsParGravite() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("select gravite, count(*) from Accident group by gravite order by count(*) desc", Object[].class)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Recherche d'accidents par critères multiples
     */
    public List<Accident> rechercherAccidents(String critereRecherche) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            String hql = "from Accident a " +
                        "left join fetch a.vehicule v " +
                        "left join fetch a.client c " +
                        "where lower(v.immatriculation) like :critere " +
                        "or lower(c.nom) like :critere " +
                        "or lower(c.prenom) like :critere " +
                        "or lower(a.lieuAccident) like :critere " +
                        "or lower(a.villeAccident) like :critere " +
                        "or lower(a.numeroConstatAmiable) like :critere " +
                        "or lower(a.numeroDeclarationPolice) like :critere " +
                        "order by a.dateAccident desc";
            
            return session.createQuery(hql, Accident.class)
                    .setParameter("critere", "%" + critereRecherche.toLowerCase() + "%")
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Met à jour le statut d'un accident
     */
    public void updateStatut(Long accidentId, Accident.StatutAccident nouveauStatut) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            Accident accident = session.get(Accident.class, accidentId);
            if (accident != null) {
                accident.setStatut(nouveauStatut);
                session.merge(accident);
            }
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    /**
     * Statistiques des accidents
     */
    public Object[] getStatistiquesAccidents() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            String hql = "select " +
                        "count(*) as total, " +
                        "sum(case when gravite in ('GRAVE', 'TRES_GRAVE', 'MORTEL') then 1 else 0 end) as graves, " +
                        "sum(case when responsabilite = 'CLIENT_RESPONSABLE' then 1 else 0 end) as clientResponsable, " +
                        "sum(case when statut in ('DECLARE', 'EN_COURS_ENQUETE', 'EXPERTISE_EN_COURS') then 1 else 0 end) as enCours, " +
                        "sum(nombreBlessesLegers + nombreBlessesGraves) as totalBlesses, " +
                        "sum(nombreDeces) as totalDeces, " +
                        "avg(estimationDegats) as coutMoyenDegats " +
                        "from Accident";
            
            return (Object[]) session.createQuery(hql).uniqueResult();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les véhicules avec le plus d'accidents
     */
    public List<Object[]> findVehiculesAvecPlusAccidents(int limite) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            String hql = "select v, count(a) as nombreAccidents " +
                        "from Vehicule v " +
                        "join Accident a on a.vehicule = v " +
                        "group by v " +
                        "order by count(a) desc";
            
            return session.createQuery(hql, Object[].class)
                    .setMaxResults(limite)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les clients avec le plus d'accidents
     */
    public List<Object[]> findClientsAvecPlusAccidents(int limite) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            String hql = "select c, count(a) as nombreAccidents " +
                        "from Client c " +
                        "join Accident a on a.client = c " +
                        "group by c " +
                        "order by count(a) desc";
            
            return session.createQuery(hql, Object[].class)
                    .setMaxResults(limite)
                    .list();
        } finally {
            session.close();
        }
    }
}
