package dao;

import model.Prolongation;
import model.Location;
import org.hibernate.Session;
import org.hibernate.Transaction;
import util.HibernateUtil;
import java.util.List;
import java.time.LocalDate;

/**
 * DAO pour la gestion des prolongations de location
 */
public class ProlongationDAO {

    public void save(Prolongation prolongation) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.merge(prolongation);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public void delete(Prolongation prolongation) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.remove(prolongation);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public List<Prolongation> findAll() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Prolongation order by dateDemandeProlong desc", Prolongation.class).list();
        } finally {
            session.close();
        }
    }

    public Prolongation findById(Long id) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.get(Prolongation.class, id);
        } finally {
            session.close();
        }
    }

    /**
     * Trouve toutes les prolongations pour une location donnée
     */
    public List<Prolongation> findByLocation(Location location) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Prolongation where location = :location order by dateDemandeProlong desc", Prolongation.class)
                    .setParameter("location", location)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les prolongations par statut
     */
    public List<Prolongation> findByStatut(Prolongation.StatutProlong statut) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Prolongation where statut = :statut order by dateDemandeProlong desc", Prolongation.class)
                    .setParameter("statut", statut)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les prolongations en attente d'approbation
     */
    public List<Prolongation> findProlongationsEnAttente() {
        return findByStatut(Prolongation.StatutProlong.DEMANDEE);
    }

    /**
     * Trouve les prolongations approuvées
     */
    public List<Prolongation> findProlongationsApprouvees() {
        return findByStatut(Prolongation.StatutProlong.APPROUVEE);
    }

    /**
     * Trouve les prolongations par période
     */
    public List<Prolongation> findByPeriode(LocalDate dateDebut, LocalDate dateFin) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Prolongation where dateDemandeProlong between :debut and :fin order by dateDemandeProlong desc", Prolongation.class)
                    .setParameter("debut", dateDebut)
                    .setParameter("dateFin", dateFin)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les prolongations en retard (demandées après la date de fin prévue)
     */
    public List<Prolongation> findProlongationsEnRetard() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Prolongation p where p.dateDemandeProlong > p.ancienneDateFin order by p.dateDemandeProlong desc", Prolongation.class)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les prolongations par agent approbateur
     */
    public List<Prolongation> findByAgentApprouveur(String agentNom) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Prolongation where agentApprouveur = :agent order by dateApprouvee desc", Prolongation.class)
                    .setParameter("agent", agentNom)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Calcule le montant total des prolongations pour une période
     */
    public Double calculerMontantTotalProlongations(LocalDate dateDebut, LocalDate dateFin) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            Double result = session.createQuery("select sum(p.prixProlong) from Prolongation p where p.dateDemandeProlong between :debut and :fin and p.statut = :statut", Double.class)
                    .setParameter("debut", dateDebut)
                    .setParameter("dateFin", dateFin)
                    .setParameter("statut", Prolongation.StatutProlong.APPROUVEE)
                    .uniqueResult();
            return result != null ? result : 0.0;
        } finally {
            session.close();
        }
    }

    /**
     * Compte le nombre de prolongations par statut
     */
    public Long compterProlongationsParStatut(Prolongation.StatutProlong statut) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("select count(*) from Prolongation where statut = :statut", Long.class)
                    .setParameter("statut", statut)
                    .uniqueResult();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les prolongations nécessitant une attention (en retard, non approuvées depuis X jours)
     */
    public List<Prolongation> findProlongationsNecessitantAttention(int joursLimite) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            LocalDate dateLimite = LocalDate.now().minusDays(joursLimite);
            return session.createQuery("from Prolongation where statut = :statut and dateDemandeProlong <= :dateLimite order by dateDemandeProlong", Prolongation.class)
                    .setParameter("statut", Prolongation.StatutProlong.DEMANDEE)
                    .setParameter("dateLimite", dateLimite)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Met à jour le statut d'une prolongation
     */
    public void updateStatut(Long prolongationId, Prolongation.StatutProlong nouveauStatut, String agentNom) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            Prolongation prolongation = session.get(Prolongation.class, prolongationId);
            if (prolongation != null) {
                prolongation.setStatut(nouveauStatut);
                if (nouveauStatut == Prolongation.StatutProlong.APPROUVEE) {
                    prolongation.setAgentApprouveur(agentNom);
                    prolongation.setDateApprouvee(LocalDate.now());
                }
                session.merge(prolongation);
            }
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    /**
     * Recherche de prolongations par critères multiples
     */
    public List<Prolongation> rechercherProlongations(String critereRecherche) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            String hql = "from Prolongation p " +
                        "left join fetch p.location l " +
                        "left join fetch l.client c " +
                        "left join fetch l.vehicule v " +
                        "where lower(c.nom) like :critere " +
                        "or lower(c.prenom) like :critere " +
                        "or lower(v.immatriculation) like :critere " +
                        "or lower(p.motifProlong) like :critere " +
                        "order by p.dateDemandeProlong desc";
            
            return session.createQuery(hql, Prolongation.class)
                    .setParameter("critere", "%" + critereRecherche.toLowerCase() + "%")
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Statistiques des prolongations
     */
    public Object[] getStatistiquesProlongations() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            String hql = "select " +
                        "count(*) as total, " +
                        "sum(case when statut = 'DEMANDEE' then 1 else 0 end) as enAttente, " +
                        "sum(case when statut = 'APPROUVEE' then 1 else 0 end) as approuvees, " +
                        "sum(case when statut = 'REFUSEE' then 1 else 0 end) as refusees, " +
                        "avg(prixProlong) as prixMoyen " +
                        "from Prolongation";
            
            return (Object[]) session.createQuery(hql).uniqueResult();
        } finally {
            session.close();
        }
    }
}
