package dao;

import model.Maintenance;
import model.Vehicule;
import org.hibernate.Session;
import org.hibernate.Transaction;
import util.HibernateUtil;
import java.util.List;
import java.time.LocalDate;

/**
 * DAO pour la gestion de la maintenance des véhicules
 */
public class MaintenanceDAO {

    public void save(Maintenance maintenance) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.merge(maintenance);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public void delete(Maintenance maintenance) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.remove(maintenance);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public List<Maintenance> findAll() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Maintenance order by dateProgrammee desc", Maintenance.class).list();
        } finally {
            session.close();
        }
    }

    public Maintenance findById(Long id) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.get(Maintenance.class, id);
        } finally {
            session.close();
        }
    }

    /**
     * Trouve toutes les maintenances pour un véhicule donné
     */
    public List<Maintenance> findByVehicule(Vehicule vehicule) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Maintenance where vehicule = :vehicule order by dateProgrammee desc", Maintenance.class)
                    .setParameter("vehicule", vehicule)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les maintenances par statut
     */
    public List<Maintenance> findByStatut(Maintenance.StatutMaintenance statut) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Maintenance where statut = :statut order by dateProgrammee", Maintenance.class)
                    .setParameter("statut", statut)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les maintenances par type
     */
    public List<Maintenance> findByType(Maintenance.TypeMaintenance type) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Maintenance where typeMaintenance = :type order by dateProgrammee desc", Maintenance.class)
                    .setParameter("type", type)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les maintenances programmées pour aujourd'hui
     */
    public List<Maintenance> findMaintenancesAujourdhui() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            LocalDate aujourdhui = LocalDate.now();
            return session.createQuery("from Maintenance where dateProgrammee = :date and statut in ('PROGRAMMEE', 'EN_COURS') order by priorite desc", Maintenance.class)
                    .setParameter("date", aujourdhui)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les maintenances en retard
     */
    public List<Maintenance> findMaintenancesEnRetard() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            LocalDate aujourdhui = LocalDate.now();
            return session.createQuery("from Maintenance where dateProgrammee < :date and statut in ('PROGRAMMEE', 'EN_COURS') order by dateProgrammee", Maintenance.class)
                    .setParameter("date", aujourdhui)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les maintenances urgentes
     */
    public List<Maintenance> findMaintenancesUrgentes() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Maintenance where priorite in ('CRITIQUE', 'URGENTE') and statut != 'TERMINEE' order by priorite desc, dateProgrammee", Maintenance.class)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les maintenances par période
     */
    public List<Maintenance> findByPeriode(LocalDate dateDebut, LocalDate dateFin) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Maintenance where dateProgrammee between :debut and :fin order by dateProgrammee", Maintenance.class)
                    .setParameter("debut", dateDebut)
                    .setParameter("fin", dateFin)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les maintenances par garage
     */
    public List<Maintenance> findByGarage(String nomGarage) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from Maintenance where lower(nomGarage) like :garage order by dateProgrammee desc", Maintenance.class)
                    .setParameter("garage", "%" + nomGarage.toLowerCase() + "%")
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Calcule le coût total de maintenance pour une période
     */
    public Double calculerCoutTotalMaintenance(LocalDate dateDebut, LocalDate dateFin) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            Double result = session.createQuery("select sum(m.coutTotal) from Maintenance m where m.dateProgrammee between :debut and :fin and m.statut = 'TERMINEE'", Double.class)
                    .setParameter("debut", dateDebut)
                    .setParameter("fin", dateFin)
                    .uniqueResult();
            return result != null ? result : 0.0;
        } finally {
            session.close();
        }
    }

    /**
     * Calcule le coût total de maintenance pour un véhicule
     */
    public Double calculerCoutMaintenanceVehicule(Vehicule vehicule) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            Double result = session.createQuery("select sum(m.coutTotal) from Maintenance m where m.vehicule = :vehicule and m.statut = 'TERMINEE'", Double.class)
                    .setParameter("vehicule", vehicule)
                    .uniqueResult();
            return result != null ? result : 0.0;
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les véhicules nécessitant une maintenance préventive
     */
    public List<Object[]> findVehiculesNecessitantMaintenance() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            String hql = "select v, max(m.prochaineMaintenance), max(m.prochainKilometrageMaintenance) " +
                        "from Vehicule v " +
                        "left join Maintenance m on m.vehicule = v " +
                        "where (m.prochaineMaintenance <= :dateLimit or " +
                        "       (m.prochainKilometrageMaintenance is not null and v.metrage >= m.prochainKilometrageMaintenance)) " +
                        "group by v " +
                        "order by v.immatriculation";
            
            LocalDate dateLimit = LocalDate.now().plusDays(30); // 30 jours d'avance
            return session.createQuery(hql, Object[].class)
                    .setParameter("dateLimit", dateLimit)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Compte les maintenances par statut
     */
    public Long compterMaintenancesParStatut(Maintenance.StatutMaintenance statut) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("select count(*) from Maintenance where statut = :statut", Long.class)
                    .setParameter("statut", statut)
                    .uniqueResult();
        } finally {
            session.close();
        }
    }

    /**
     * Recherche de maintenances par critères multiples
     */
    public List<Maintenance> rechercherMaintenances(String critereRecherche) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            String hql = "from Maintenance m " +
                        "left join fetch m.vehicule v " +
                        "where lower(v.immatriculation) like :critere " +
                        "or lower(v.marque) like :critere " +
                        "or lower(v.modele) like :critere " +
                        "or lower(m.description) like :critere " +
                        "or lower(m.nomGarage) like :critere " +
                        "order by m.dateProgrammee desc";
            
            return session.createQuery(hql, Maintenance.class)
                    .setParameter("critere", "%" + critereRecherche.toLowerCase() + "%")
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Met à jour le statut d'une maintenance
     */
    public void updateStatut(Long maintenanceId, Maintenance.StatutMaintenance nouveauStatut) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            Maintenance maintenance = session.get(Maintenance.class, maintenanceId);
            if (maintenance != null) {
                maintenance.setStatut(nouveauStatut);
                if (nouveauStatut == Maintenance.StatutMaintenance.EN_COURS) {
                    maintenance.setDateDebut(LocalDate.now());
                } else if (nouveauStatut == Maintenance.StatutMaintenance.TERMINEE) {
                    maintenance.setDateFin(LocalDate.now());
                }
                session.merge(maintenance);
            }
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    /**
     * Statistiques de maintenance
     */
    public Object[] getStatistiquesMaintenance() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            String hql = "select " +
                        "count(*) as total, " +
                        "sum(case when statut = 'PROGRAMMEE' then 1 else 0 end) as programmees, " +
                        "sum(case when statut = 'EN_COURS' then 1 else 0 end) as enCours, " +
                        "sum(case when statut = 'TERMINEE' then 1 else 0 end) as terminees, " +
                        "sum(case when priorite in ('CRITIQUE', 'URGENTE') then 1 else 0 end) as urgentes, " +
                        "avg(coutTotal) as coutMoyen " +
                        "from Maintenance";
            
            return (Object[]) session.createQuery(hql).uniqueResult();
        } finally {
            session.close();
        }
    }

    /**
     * Planifier la prochaine maintenance préventive
     */
    public void planifierProchaineMaintenance(Vehicule vehicule, Maintenance.TypeMaintenance type, 
                                            LocalDate dateProchaine, Integer kilometrageProchain) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            Maintenance nouvelleMaintenance = new Maintenance(vehicule, type, "Maintenance préventive programmée");
            nouvelleMaintenance.setDateProgrammee(dateProchaine);
            nouvelleMaintenance.setProchainKilometrageMaintenance(kilometrageProchain);
            nouvelleMaintenance.setStatut(Maintenance.StatutMaintenance.PROGRAMMEE);
            nouvelleMaintenance.setPriorite(Maintenance.NiveauPriorite.NORMALE);
            
            session.persist(nouvelleMaintenance);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }
}
