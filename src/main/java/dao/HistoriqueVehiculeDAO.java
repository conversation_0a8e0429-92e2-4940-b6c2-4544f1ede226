package dao;

import model.HistoriqueVehicule;
import model.Vehicule;
import model.Location;
import model.Client;
import org.hibernate.Session;
import org.hibernate.Transaction;
import util.HibernateUtil;
import java.util.List;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * DAO pour la gestion de l'historique des véhicules
 */
public class HistoriqueVehiculeDAO {

    public void save(HistoriqueVehicule historique) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.merge(historique);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public void delete(HistoriqueVehicule historique) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            session.remove(historique);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    public List<HistoriqueVehicule> findAll() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from HistoriqueVehicule order by dateEvenement desc", HistoriqueVehicule.class).list();
        } finally {
            session.close();
        }
    }

    public HistoriqueVehicule findById(Long id) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.get(HistoriqueVehicule.class, id);
        } finally {
            session.close();
        }
    }

    /**
     * Trouve tout l'historique pour un véhicule donné
     */
    public List<HistoriqueVehicule> findByVehicule(Vehicule vehicule) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from HistoriqueVehicule where vehicule = :vehicule order by dateEvenement desc", HistoriqueVehicule.class)
                    .setParameter("vehicule", vehicule)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve l'historique par type d'événement
     */
    public List<HistoriqueVehicule> findByTypeEvenement(HistoriqueVehicule.TypeEvenement type) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from HistoriqueVehicule where typeEvenement = :type order by dateEvenement desc", HistoriqueVehicule.class)
                    .setParameter("type", type)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve l'historique par période
     */
    public List<HistoriqueVehicule> findByPeriode(LocalDate dateDebut, LocalDate dateFin) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from HistoriqueVehicule where date(dateEvenement) between :debut and :fin order by dateEvenement desc", HistoriqueVehicule.class)
                    .setParameter("debut", dateDebut)
                    .setParameter("fin", dateFin)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve l'historique pour un véhicule par période
     */
    public List<HistoriqueVehicule> findByVehiculeEtPeriode(Vehicule vehicule, LocalDate dateDebut, LocalDate dateFin) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from HistoriqueVehicule where vehicule = :vehicule and date(dateEvenement) between :debut and :fin order by dateEvenement desc", HistoriqueVehicule.class)
                    .setParameter("vehicule", vehicule)
                    .setParameter("debut", dateDebut)
                    .setParameter("fin", dateFin)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve l'historique par client
     */
    public List<HistoriqueVehicule> findByClient(Client client) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from HistoriqueVehicule where client = :client order by dateEvenement desc", HistoriqueVehicule.class)
                    .setParameter("client", client)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve l'historique par location
     */
    public List<HistoriqueVehicule> findByLocation(Location location) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from HistoriqueVehicule where location = :location order by dateEvenement desc", HistoriqueVehicule.class)
                    .setParameter("location", location)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les événements financiers (avec coût)
     */
    public List<HistoriqueVehicule> findEvenementsFinanciers() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from HistoriqueVehicule where coutEvenement is not null and coutEvenement > 0 order by dateEvenement desc", HistoriqueVehicule.class)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les événements financiers pour un véhicule
     */
    public List<HistoriqueVehicule> findEvenementsFinanciersVehicule(Vehicule vehicule) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from HistoriqueVehicule where vehicule = :vehicule and coutEvenement is not null and coutEvenement > 0 order by dateEvenement desc", HistoriqueVehicule.class)
                    .setParameter("vehicule", vehicule)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les accidents dans l'historique
     */
    public List<HistoriqueVehicule> findAccidents() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from HistoriqueVehicule where typeEvenement in ('ACCIDENT', 'SINISTRE', 'VOL') order by dateEvenement desc", HistoriqueVehicule.class)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les maintenances dans l'historique
     */
    public List<HistoriqueVehicule> findMaintenances() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("from HistoriqueVehicule where typeEvenement in ('MAINTENANCE', 'REPARATION', 'CONTROLE_TECHNIQUE') order by dateEvenement desc", HistoriqueVehicule.class)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Calcule le coût total des événements pour un véhicule
     */
    public Double calculerCoutTotalVehicule(Vehicule vehicule) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            Double result = session.createQuery("select sum(h.coutEvenement) from HistoriqueVehicule h where h.vehicule = :vehicule and h.coutEvenement is not null", Double.class)
                    .setParameter("vehicule", vehicule)
                    .uniqueResult();
            return result != null ? result : 0.0;
        } finally {
            session.close();
        }
    }

    /**
     * Calcule le coût total par type d'événement
     */
    public Double calculerCoutParType(HistoriqueVehicule.TypeEvenement type, LocalDate dateDebut, LocalDate dateFin) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            Double result = session.createQuery("select sum(h.coutEvenement) from HistoriqueVehicule h where h.typeEvenement = :type and date(h.dateEvenement) between :debut and :fin and h.coutEvenement is not null", Double.class)
                    .setParameter("type", type)
                    .setParameter("debut", dateDebut)
                    .setParameter("fin", dateFin)
                    .uniqueResult();
            return result != null ? result : 0.0;
        } finally {
            session.close();
        }
    }

    /**
     * Calcule le kilométrage total parcouru par un véhicule
     */
    public Integer calculerKilometrageTotalVehicule(Vehicule vehicule) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            Integer result = session.createQuery("select sum(h.kilometrageParcouru) from HistoriqueVehicule h where h.vehicule = :vehicule and h.kilometrageParcouru is not null", Integer.class)
                    .setParameter("vehicule", vehicule)
                    .uniqueResult();
            return result != null ? result : 0;
        } finally {
            session.close();
        }
    }

    /**
     * Trouve le dernier événement pour un véhicule
     */
    public HistoriqueVehicule findDernierEvenementVehicule(Vehicule vehicule) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            List<HistoriqueVehicule> result = session.createQuery("from HistoriqueVehicule where vehicule = :vehicule order by dateEvenement desc", HistoriqueVehicule.class)
                    .setParameter("vehicule", vehicule)
                    .setMaxResults(1)
                    .list();
            return result.isEmpty() ? null : result.get(0);
        } finally {
            session.close();
        }
    }

    /**
     * Trouve le dernier événement d'un type spécifique pour un véhicule
     */
    public HistoriqueVehicule findDernierEvenementParType(Vehicule vehicule, HistoriqueVehicule.TypeEvenement type) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            List<HistoriqueVehicule> result = session.createQuery("from HistoriqueVehicule where vehicule = :vehicule and typeEvenement = :type order by dateEvenement desc", HistoriqueVehicule.class)
                    .setParameter("vehicule", vehicule)
                    .setParameter("type", type)
                    .setMaxResults(1)
                    .list();
            return result.isEmpty() ? null : result.get(0);
        } finally {
            session.close();
        }
    }

    /**
     * Recherche dans l'historique par critères multiples
     */
    public List<HistoriqueVehicule> rechercherHistorique(String critereRecherche) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            String hql = "from HistoriqueVehicule h " +
                        "left join fetch h.vehicule v " +
                        "left join fetch h.client c " +
                        "where lower(v.immatriculation) like :critere " +
                        "or lower(v.marque) like :critere " +
                        "or lower(v.modele) like :critere " +
                        "or lower(h.description) like :critere " +
                        "or lower(h.fournisseur) like :critere " +
                        "or lower(h.numeroFacture) like :critere " +
                        "or lower(h.numeroRapport) like :critere " +
                        "order by h.dateEvenement desc";
            
            return session.createQuery(hql, HistoriqueVehicule.class)
                    .setParameter("critere", "%" + critereRecherche.toLowerCase() + "%")
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Statistiques de l'historique
     */
    public Object[] getStatistiquesHistorique() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            String hql = "select " +
                        "count(*) as totalEvenements, " +
                        "count(distinct vehicule) as vehiculesConcernes, " +
                        "sum(case when coutEvenement is not null then coutEvenement else 0 end) as coutTotal, " +
                        "avg(case when coutEvenement is not null then coutEvenement else null end) as coutMoyen, " +
                        "sum(case when typeEvenement = 'ACCIDENT' then 1 else 0 end) as nombreAccidents, " +
                        "sum(case when typeEvenement = 'MAINTENANCE' then 1 else 0 end) as nombreMaintenances " +
                        "from HistoriqueVehicule";
            
            return (Object[]) session.createQuery(hql).uniqueResult();
        } finally {
            session.close();
        }
    }

    /**
     * Compte les événements par type
     */
    public List<Object[]> compterEvenementsParType() {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            return session.createQuery("select typeEvenement, count(*) from HistoriqueVehicule group by typeEvenement order by count(*) desc", Object[].class)
                    .list();
        } finally {
            session.close();
        }
    }

    /**
     * Ajoute un événement à l'historique d'un véhicule
     */
    public void ajouterEvenement(Vehicule vehicule, HistoriqueVehicule.TypeEvenement type, 
                                String description, String details, Double cout, 
                                Integer kilometrageAvant, Integer kilometrageApres) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        Transaction tx = session.beginTransaction();
        try {
            HistoriqueVehicule historique = new HistoriqueVehicule(vehicule, type, description);
            historique.setDetailsEvenement(details);
            historique.setCoutEvenement(cout);
            historique.setKilometrageAvant(kilometrageAvant);
            historique.setKilometrageApres(kilometrageApres);
            
            session.persist(historique);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            throw e;
        } finally {
            session.close();
        }
    }

    /**
     * Trouve les véhicules avec le plus d'événements
     */
    public List<Object[]> findVehiculesAvecPlusEvenements(int limite) {
        Session session = HibernateUtil.getSessionFactory().openSession();
        try {
            String hql = "select v, count(h) as nombreEvenements " +
                        "from Vehicule v " +
                        "join HistoriqueVehicule h on h.vehicule = v " +
                        "group by v " +
                        "order by count(h) desc";
            
            return session.createQuery(hql, Object[].class)
                    .setMaxResults(limite)
                    .list();
        } finally {
            session.close();
        }
    }
}
