package test;

import dao.*;
import model.*;
import util.HibernateUtil;
import util.DatabaseInitializer;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Test des nouvelles fonctionnalités ajoutées
 * Vérifie que tous les DAOs et modèles fonctionnent correctement
 */
public class TestNouvellesFonctionnalites {

    public static void main(String[] args) {
        System.out.println("=== Test des Nouvelles Fonctionnalités ===");
        
        try {
            // Initialiser la base de données
            DatabaseInitializer.initialize();
            
            // Test des DAOs
            testProlongationDAO();
            testMaintenanceDAO();
            testAccidentDAO();
            testHistoriqueVehiculeDAO();
            
            System.out.println("\n✅ Tous les tests sont passés avec succès!");
            
        } catch (Exception e) {
            System.err.println("❌ Erreur lors des tests: " + e.getMessage());
            e.printStackTrace();
        } finally {
            HibernateUtil.shutdown();
        }
    }
    
    private static void testProlongationDAO() {
        System.out.println("\n--- Test ProlongationDAO ---");
        
        ProlongationDAO prolongationDAO = new ProlongationDAO();
        LocationDAO locationDAO = new LocationDAO();
        
        try {
            // Récupérer une location existante
            List<Location> locations = locationDAO.findAll();
            if (!locations.isEmpty()) {
                Location location = locations.get(0);
                
                // Créer une nouvelle prolongation
                Prolongation prolongation = new Prolongation();
                prolongation.setLocation(location);
                prolongation.setAncienneDateFin(location.getDateFinPrevue());
                prolongation.setNouvelleDateFin(location.getDateFinPrevue().plusDays(3));
                prolongation.setDateDemandeProlong(LocalDate.now());
                prolongation.setMotifProlong("Test de prolongation");
                prolongation.setPrixProlong(150.0);
                prolongation.setVerificationPermisValide(true);
                prolongation.setVerificationAssuranceValide(true);
                prolongation.setControleVehicule(true);
                
                // Sauvegarder
                prolongationDAO.save(prolongation);
                System.out.println("✓ Prolongation créée avec ID: " + prolongation.getId());
                
                // Tester les méthodes de recherche
                List<Prolongation> prolongations = prolongationDAO.findAll();
                System.out.println("✓ Nombre total de prolongations: " + prolongations.size());
                
                List<Prolongation> prolongationsLocation = prolongationDAO.findByLocation(location);
                System.out.println("✓ Prolongations pour cette location: " + prolongationsLocation.size());
                
                // Approuver la prolongation
                prolongation.approuver("admin");
                prolongationDAO.save(prolongation);
                System.out.println("✓ Prolongation approuvée");
                
            } else {
                System.out.println("⚠ Aucune location trouvée pour tester les prolongations");
            }
            
        } catch (Exception e) {
            System.err.println("❌ Erreur dans testProlongationDAO: " + e.getMessage());
            throw e;
        }
    }
    
    private static void testMaintenanceDAO() {
        System.out.println("\n--- Test MaintenanceDAO ---");
        
        MaintenanceDAO maintenanceDAO = new MaintenanceDAO();
        VehiculeDAO vehiculeDAO = new VehiculeDAO();
        
        try {
            // Récupérer un véhicule existant
            List<Vehicule> vehicules = vehiculeDAO.findAll();
            if (!vehicules.isEmpty()) {
                Vehicule vehicule = vehicules.get(0);
                
                // Créer une nouvelle maintenance
                Maintenance maintenance = new Maintenance();
                maintenance.setVehicule(vehicule);
                maintenance.setTypeMaintenance(Maintenance.TypeMaintenance.PREVENTIVE);
                maintenance.setStatut(Maintenance.StatutMaintenance.PROGRAMMEE);
                maintenance.setDateProgrammee(LocalDate.now().plusDays(7));
                maintenance.setDescription("Maintenance préventive de test");
                maintenance.setKilometrageActuel(vehicule.getMetrage());
                maintenance.setPriorite(Maintenance.NiveauPriorite.NORMALE);
                maintenance.setNomGarage("Garage Test");
                maintenance.setCreatedBy("admin");
                
                // Sauvegarder
                maintenanceDAO.save(maintenance);
                System.out.println("✓ Maintenance créée avec ID: " + maintenance.getId());
                
                // Tester les méthodes de recherche
                List<Maintenance> maintenances = maintenanceDAO.findAll();
                System.out.println("✓ Nombre total de maintenances: " + maintenances.size());
                
                List<Maintenance> maintenancesVehicule = maintenanceDAO.findByVehicule(vehicule);
                System.out.println("✓ Maintenances pour ce véhicule: " + maintenancesVehicule.size());
                
                // Commencer la maintenance
                maintenanceDAO.updateStatut(maintenance.getId(), Maintenance.StatutMaintenance.EN_COURS);
                System.out.println("✓ Maintenance mise en cours");
                
                // Terminer la maintenance
                maintenance.terminerMaintenance("Travaux effectués", "Pièces changées", "Véhicule OK");
                maintenanceDAO.save(maintenance);
                System.out.println("✓ Maintenance terminée");
                
            } else {
                System.out.println("⚠ Aucun véhicule trouvé pour tester la maintenance");
            }
            
        } catch (Exception e) {
            System.err.println("❌ Erreur dans testMaintenanceDAO: " + e.getMessage());
            throw e;
        }
    }
    
    private static void testAccidentDAO() {
        System.out.println("\n--- Test AccidentDAO ---");
        
        AccidentDAO accidentDAO = new AccidentDAO();
        VehiculeDAO vehiculeDAO = new VehiculeDAO();
        LocationDAO locationDAO = new LocationDAO();
        
        try {
            // Récupérer un véhicule et une location existants
            List<Vehicule> vehicules = vehiculeDAO.findAll();
            List<Location> locations = locationDAO.findAll();
            
            if (!vehicules.isEmpty() && !locations.isEmpty()) {
                Vehicule vehicule = vehicules.get(0);
                Location location = locations.get(0);
                
                // Créer un nouvel accident
                Accident accident = new Accident();
                accident.setVehicule(vehicule);
                accident.setLocation(location);
                accident.setClient(location.getClient());
                accident.setDateAccident(LocalDate.now().minusDays(1));
                accident.setLieuAccident("Avenue Mohammed V, Casablanca");
                accident.setVilleAccident("Casablanca");
                accident.setTypeAccident(Accident.TypeAccident.COLLISION_ARRIERE);
                accident.setGravite(Accident.GraviteAccident.LEGER);
                accident.setDescriptionAccident("Accident de test - collision légère");
                accident.setResponsabilite(Accident.Responsabilite.TIERS_RESPONSABLE);
                accident.setEstimationDegats(500.0);
                accident.setCompagnieAssurance("Assurance Test");
                accident.setAgentDeclarant("admin");
                
                // Sauvegarder
                accidentDAO.save(accident);
                System.out.println("✓ Accident créé avec ID: " + accident.getId());
                
                // Tester les méthodes de recherche
                List<Accident> accidents = accidentDAO.findAll();
                System.out.println("✓ Nombre total d'accidents: " + accidents.size());
                
                List<Accident> accidentsVehicule = accidentDAO.findByVehicule(vehicule);
                System.out.println("✓ Accidents pour ce véhicule: " + accidentsVehicule.size());
                
                // Déclarer à l'assurance
                accident.declareALAssurance("DECL-TEST-001", "SIN-TEST-001");
                accidentDAO.save(accident);
                System.out.println("✓ Accident déclaré à l'assurance");
                
            } else {
                System.out.println("⚠ Véhicules ou locations manquants pour tester les accidents");
            }
            
        } catch (Exception e) {
            System.err.println("❌ Erreur dans testAccidentDAO: " + e.getMessage());
            throw e;
        }
    }
    
    private static void testHistoriqueVehiculeDAO() {
        System.out.println("\n--- Test HistoriqueVehiculeDAO ---");
        
        HistoriqueVehiculeDAO historiqueDAO = new HistoriqueVehiculeDAO();
        VehiculeDAO vehiculeDAO = new VehiculeDAO();
        
        try {
            // Récupérer un véhicule existant
            List<Vehicule> vehicules = vehiculeDAO.findAll();
            if (!vehicules.isEmpty()) {
                Vehicule vehicule = vehicules.get(0);
                
                // Créer un nouvel événement d'historique
                HistoriqueVehicule historique = new HistoriqueVehicule();
                historique.setVehicule(vehicule);
                historique.setTypeEvenement(HistoriqueVehicule.TypeEvenement.MAINTENANCE);
                historique.setDateEvenement(LocalDateTime.now());
                historique.setDescription("Test de maintenance dans l'historique");
                historique.setDetailsEvenement("Détails de la maintenance de test");
                historique.setCoutEvenement(200.0);
                historique.setFournisseur("Garage Test");
                historique.setCreatedBy("admin");
                
                // Sauvegarder
                historiqueDAO.save(historique);
                System.out.println("✓ Événement d'historique créé avec ID: " + historique.getId());
                
                // Tester les méthodes de recherche
                List<HistoriqueVehicule> historiques = historiqueDAO.findAll();
                System.out.println("✓ Nombre total d'événements: " + historiques.size());
                
                List<HistoriqueVehicule> historiquesVehicule = historiqueDAO.findByVehicule(vehicule);
                System.out.println("✓ Événements pour ce véhicule: " + historiquesVehicule.size());
                
                // Calculer le coût total pour ce véhicule
                Double coutTotal = historiqueDAO.calculerCoutTotalVehicule(vehicule);
                System.out.println("✓ Coût total pour ce véhicule: " + coutTotal + " DH");
                
                // Statistiques
                Object[] stats = historiqueDAO.getStatistiquesHistorique();
                if (stats != null && stats.length > 0) {
                    System.out.println("✓ Statistiques récupérées: " + stats[0] + " événements");
                }
                
            } else {
                System.out.println("⚠ Aucun véhicule trouvé pour tester l'historique");
            }
            
        } catch (Exception e) {
            System.err.println("❌ Erreur dans testHistoriqueVehiculeDAO: " + e.getMessage());
            throw e;
        }
    }
}
