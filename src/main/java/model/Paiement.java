package model;

import jakarta.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Entité Paiement - Gestion des paiements de location
 * Conforme aux standards comptables et fiscaux marocains
 */
@Entity
@Table(name = "paiement")
public class Paiement {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "location_id", nullable = false)
    private Location location;

    // Informations de base du paiement
    private double montant;
    private LocalDate datePaiement;
    private LocalDateTime heureTransaction; // Heure précise de la transaction

    // Type et méthode de paiement
    public enum TypePaiement {
        ACOMPTE,           // Acompte à la réservation
        SOLDE,             // Solde à la prise du véhicule
        CAUTION,           // Caution/dépôt de garantie
        PENALITE,          // Paiement de pénalité
        SUPPLEMENT,        // Supplément (options, kilométrage, etc.)
        REMBOURSEMENT,     // Remboursement (annulation, caution)
        PROLONGATION,      // Paiement pour prolongation
        REPARATION         // Paiement pour réparations
    }

    @Enumerated(EnumType.STRING)
    private TypePaiement typePaiement;

    public enum MethodePaiement {
        ESPECES,           // Paiement en espèces
        CARTE_BANCAIRE,    // Carte bancaire
        CHEQUE,            // Chèque
        VIREMENT,          // Virement bancaire
        MOBILE_MONEY,      // Paiement mobile (Orange Money, etc.)
        CARTE_CREDIT,      // Carte de crédit
        PAYPAL,            // PayPal
        AUTRE              // Autre méthode
    }

    @Enumerated(EnumType.STRING)
    private MethodePaiement methodePaiement;

    // Statut du paiement
    public enum StatutPaiement {
        EN_ATTENTE,        // En attente de traitement
        VALIDE,            // Paiement validé
        REFUSE,            // Paiement refusé
        ANNULE,            // Paiement annulé
        REMBOURSE,         // Paiement remboursé
        EN_COURS,          // Transaction en cours
        EXPIRE             // Paiement expiré
    }

    @Enumerated(EnumType.STRING)
    private StatutPaiement statut;

    // Détails de la transaction
    private String numeroTransaction; // Numéro de transaction bancaire
    private String numeroRecu; // Numéro de reçu
    private String referenceInterne; // Référence interne de l'entreprise
    private String numeroAutorisation; // Numéro d'autorisation bancaire

    // Informations bancaires (si applicable)
    private String banqueEmettrice; // Banque émettrice
    private String numeroCompte; // Numéro de compte (masqué)
    private String nomPorteurCarte; // Nom du porteur de carte
    private String dernierQuatreChiffres; // 4 derniers chiffres de la carte

    // Détails comptables et fiscaux
    private double montantHT; // Montant hors taxes
    private double montantTVA; // Montant TVA
    private double tauxTVA; // Taux de TVA appliqué (20% au Maroc)
    private String numeroFacture; // Numéro de facture associée
    private boolean factureEmise; // Facture émise

    // Informations de change (pour clients étrangers)
    private String deviseOriginale; // Devise d'origine
    private double montantDeviseOriginale; // Montant en devise d'origine
    private double tauxChange; // Taux de change appliqué

    // Frais et commissions
    private double fraisBancaires; // Frais bancaires
    private double commission; // Commission de l'intermédiaire
    private double montantNet; // Montant net reçu

    // Informations de remboursement
    private boolean remboursable; // Paiement remboursable
    private LocalDate dateRemboursement; // Date de remboursement
    private double montantRembourse; // Montant remboursé
    private String motifRemboursement; // Motif du remboursement

    // Métadonnées
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String agentEncaisseur; // Agent qui a encaissé
    private String agentValidateur; // Agent qui a validé
    private String commentaires; // Commentaires sur le paiement

    // Constructeurs
    public Paiement() {
        this.createdAt = LocalDateTime.now();
        this.heureTransaction = LocalDateTime.now();
        this.statut = StatutPaiement.EN_ATTENTE;
        this.tauxTVA = 20.0; // TVA standard au Maroc
    }

    public Paiement(Location location, double montant, TypePaiement type, MethodePaiement methode) {
        this();
        this.location = location;
        this.montant = montant;
        this.typePaiement = type;
        this.methodePaiement = methode;
        this.datePaiement = LocalDate.now();
        calculerMontantsTVA();
    }

    // Getters et setters de base
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Location getLocation() { return location; }
    public void setLocation(Location location) { this.location = location; }

    public double getMontant() { return montant; }
    public void setMontant(double montant) {
        this.montant = montant;
        calculerMontantsTVA();
    }

    public LocalDate getDatePaiement() { return datePaiement; }
    public void setDatePaiement(LocalDate datePaiement) { this.datePaiement = datePaiement; }

    public LocalDateTime getHeureTransaction() { return heureTransaction; }
    public void setHeureTransaction(LocalDateTime heureTransaction) { this.heureTransaction = heureTransaction; }

    public TypePaiement getTypePaiement() { return typePaiement; }
    public void setTypePaiement(TypePaiement typePaiement) { this.typePaiement = typePaiement; }

    public MethodePaiement getMethodePaiement() { return methodePaiement; }
    public void setMethodePaiement(MethodePaiement methodePaiement) { this.methodePaiement = methodePaiement; }

    public StatutPaiement getStatut() { return statut; }
    public void setStatut(StatutPaiement statut) {
        this.statut = statut;
        this.updatedAt = LocalDateTime.now();
    }

    public String getNumeroTransaction() { return numeroTransaction; }
    public void setNumeroTransaction(String numeroTransaction) { this.numeroTransaction = numeroTransaction; }

    public String getNumeroRecu() { return numeroRecu; }
    public void setNumeroRecu(String numeroRecu) { this.numeroRecu = numeroRecu; }

    public String getReferenceInterne() { return referenceInterne; }
    public void setReferenceInterne(String referenceInterne) { this.referenceInterne = referenceInterne; }

    public String getNumeroAutorisation() { return numeroAutorisation; }
    public void setNumeroAutorisation(String numeroAutorisation) { this.numeroAutorisation = numeroAutorisation; }

    // Méthodes utilitaires
    private void calculerMontantsTVA() {
        if (montant > 0 && tauxTVA > 0) {
            this.montantHT = montant / (1 + tauxTVA / 100);
            this.montantTVA = montant - montantHT;
        }
    }

    public boolean estValide() {
        return statut == StatutPaiement.VALIDE;
    }

    public boolean estRemboursable() {
        return remboursable && estValide() && montantRembourse == 0;
    }

    public void valider(String agentNom) {
        this.statut = StatutPaiement.VALIDE;
        this.agentValidateur = agentNom;
        this.updatedAt = LocalDateTime.now();
    }

    public void rembourser(double montant, String motif) {
        if (montant <= this.montant && estRemboursable()) {
            this.montantRembourse = montant;
            this.motifRemboursement = motif;
            this.dateRemboursement = LocalDate.now();
            this.statut = StatutPaiement.REMBOURSE;
            this.updatedAt = LocalDateTime.now();
        }
    }

    @Override
    public String toString() {
        return String.format("Paiement[id=%d, montant=%.2f DH, type=%s, statut=%s, date=%s]",
                           id, montant, typePaiement, statut, datePaiement);
    }
}