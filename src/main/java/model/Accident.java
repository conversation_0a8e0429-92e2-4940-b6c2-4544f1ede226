package model;

import jakarta.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * Entité Accident - Gestion des accidents de véhicules
 * Conforme à la réglementation marocaine des assurances et déclarations d'accidents
 */
@Entity
@Table(name = "accident")
public class Accident {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "vehicule_id", nullable = false)
    private Vehicule vehicule;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "location_id")
    private Location location;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "client_id")
    private Client client;

    // Informations de base de l'accident
    private LocalDate dateAccident;
    private LocalTime heureAccident;
    private String lieuAccident; // Adresse précise
    private String villeAccident;
    private String coordonneesGPS; // Coordonnées GPS si disponibles

    // Type et gravité de l'accident
    public enum TypeAccident {
        COLLISION_FRONTALE,
        COLLISION_ARRIERE,
        COLLISION_LATERALE,
        SORTIE_ROUTE,
        RENVERSEMENT,
        CHOC_OBSTACLE,
        INCENDIE,
        VOL,
        VANDALISME,
        CATASTROPHE_NATURELLE,
        AUTRE
    }

    @Enumerated(EnumType.STRING)
    private TypeAccident typeAccident;

    public enum GraviteAccident {
        LEGER,      // Dégâts matériels mineurs
        MOYEN,      // Dégâts matériels importants
        GRAVE,      // Dégâts matériels + blessés légers
        TRES_GRAVE, // Blessés graves
        MORTEL      // Accident mortel
    }

    @Enumerated(EnumType.STRING)
    private GraviteAccident gravite;

    // Description de l'accident
    private String descriptionAccident; // Description détaillée
    private String circonstancesAccident; // Circonstances
    private String conditionsMeteo; // Conditions météorologiques
    private String etatChaussee; // État de la chaussée
    private String visibilite; // Conditions de visibilité

    // Responsabilité
    public enum Responsabilite {
        CLIENT_RESPONSABLE,
        TIERS_RESPONSABLE,
        RESPONSABILITE_PARTAGEE,
        FORCE_MAJEURE,
        EN_COURS_DETERMINATION
    }

    @Enumerated(EnumType.STRING)
    private Responsabilite responsabilite;

    private String detailsResponsabilite; // Détails sur la responsabilité

    // Personnes impliquées
    private boolean blessesClient; // Client blessé
    private boolean blessesTiers; // Tiers blessés
    private int nombreBlessesLegers;
    private int nombreBlessesGraves;
    private int nombreDeces;

    // Véhicules impliqués
    private boolean autresVehiculesImpliques;
    private int nombreVehiculesImpliques;
    private String detailsAutresVehicules; // Détails des autres véhicules

    // Dégâts matériels
    private String degatsVehiculeLoue; // Dégâts sur le véhicule loué
    private String degatsAutresVehicules; // Dégâts sur les autres véhicules
    private String degatsInfrastructure; // Dégâts aux infrastructures
    private Double estimationDegats; // Estimation des dégâts en DH

    // Informations réglementaires marocaines
    private String numeroConstatAmiable; // Numéro du constat amiable
    private String numeroDeclarationPolice; // Numéro de déclaration police
    private String commissariatCompetent; // Commissariat compétent
    private String numeroProcessVerbal; // Numéro du procès-verbal
    private boolean alcoolemieEffectuee; // Test d'alcoolémie effectué
    private String resultatAlcoolemie; // Résultat du test

    // Assurance
    private String compagnieAssurance; // Compagnie d'assurance
    private String numeroPoliceAssurance; // Numéro de police
    private String numeroDeclarationAssurance; // Numéro de déclaration
    private LocalDate dateDeclarationAssurance;
    private String numeroSinistre; // Numéro de sinistre
    private boolean priseEnChargeAssurance; // Prise en charge par l'assurance

    // Témoins
    private boolean temoinsPresents;
    private String detailsTemoin1; // Nom, prénom, adresse, téléphone
    private String detailsTemoin2;
    private String detailsTemoin3;

    // Documents et preuves
    private String photosAccident; // Chemins vers les photos
    private String rapportPolice; // Chemin vers le rapport de police
    private String rapportExpertise; // Rapport d'expertise
    private String certificatsMedicaux; // Certificats médicaux si blessés
    private String autresDocuments; // Autres documents

    // Suivi et résolution
    public enum StatutAccident {
        DECLARE,           // Déclaré
        EN_COURS_ENQUETE,  // Enquête en cours
        EXPERTISE_EN_COURS, // Expertise en cours
        RESOLU,            // Résolu
        CONTENTIEUX,       // En contentieux
        CLOS               // Clos
    }

    @Enumerated(EnumType.STRING)
    private StatutAccident statut;

    private Double montantIndemnisation; // Montant d'indemnisation reçu
    private Double montantFranchise; // Montant de la franchise
    private Double montantRecupere; // Montant récupéré du tiers responsable

    // Conséquences sur le véhicule
    private boolean vehiculeReparable; // Véhicule réparable
    private boolean vehiculeEconomiquementIrreparable; // VEI
    private LocalDate dateRemiseEnService; // Date de remise en service
    private Double coutReparation; // Coût de réparation

    // Métadonnées
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String agentDeclarant; // Agent qui a déclaré l'accident
    private String derniereModificationPar;

    // Constructeurs
    public Accident() {
        this.createdAt = LocalDateTime.now();
        this.statut = StatutAccident.DECLARE;
        this.dateAccident = LocalDate.now();
        this.heureAccident = LocalTime.now();
    }

    public Accident(Vehicule vehicule, Location location, TypeAccident type) {
        this();
        this.vehicule = vehicule;
        this.location = location;
        this.client = location != null ? location.getClient() : null;
        this.typeAccident = type;
    }

    // Getters et setters (partie 1)
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Vehicule getVehicule() { return vehicule; }
    public void setVehicule(Vehicule vehicule) { this.vehicule = vehicule; }

    public Location getLocation() { return location; }
    public void setLocation(Location location) { this.location = location; }

    public Client getClient() { return client; }
    public void setClient(Client client) { this.client = client; }

    public LocalDate getDateAccident() { return dateAccident; }
    public void setDateAccident(LocalDate dateAccident) { this.dateAccident = dateAccident; }

    public LocalTime getHeureAccident() { return heureAccident; }
    public void setHeureAccident(LocalTime heureAccident) { this.heureAccident = heureAccident; }

    public String getLieuAccident() { return lieuAccident; }
    public void setLieuAccident(String lieuAccident) { this.lieuAccident = lieuAccident; }

    public String getVilleAccident() { return villeAccident; }
    public void setVilleAccident(String villeAccident) { this.villeAccident = villeAccident; }

    public String getCoordonneesGPS() { return coordonneesGPS; }
    public void setCoordonneesGPS(String coordonneesGPS) { this.coordonneesGPS = coordonneesGPS; }

    public TypeAccident getTypeAccident() { return typeAccident; }
    public void setTypeAccident(TypeAccident typeAccident) { this.typeAccident = typeAccident; }

    public GraviteAccident getGravite() { return gravite; }
    public void setGravite(GraviteAccident gravite) { this.gravite = gravite; }

    public String getDescriptionAccident() { return descriptionAccident; }
    public void setDescriptionAccident(String descriptionAccident) { this.descriptionAccident = descriptionAccident; }

    public String getCirconstancesAccident() { return circonstancesAccident; }
    public void setCirconstancesAccident(String circonstancesAccident) { this.circonstancesAccident = circonstancesAccident; }

    public String getConditionsMeteo() { return conditionsMeteo; }
    public void setConditionsMeteo(String conditionsMeteo) { this.conditionsMeteo = conditionsMeteo; }

    public String getEtatChaussee() { return etatChaussee; }
    public void setEtatChaussee(String etatChaussee) { this.etatChaussee = etatChaussee; }

    public String getVisibilite() { return visibilite; }
    public void setVisibilite(String visibilite) { this.visibilite = visibilite; }

    public Responsabilite getResponsabilite() { return responsabilite; }
    public void setResponsabilite(Responsabilite responsabilite) { this.responsabilite = responsabilite; }

    public String getDetailsResponsabilite() { return detailsResponsabilite; }
    public void setDetailsResponsabilite(String detailsResponsabilite) { this.detailsResponsabilite = detailsResponsabilite; }

    public boolean isBlessesClient() { return blessesClient; }
    public void setBlessesClient(boolean blessesClient) { this.blessesClient = blessesClient; }

    public boolean isBlessesTiers() { return blessesTiers; }
    public void setBlessesTiers(boolean blessesTiers) { this.blessesTiers = blessesTiers; }

    public int getNombreBlessesLegers() { return nombreBlessesLegers; }
    public void setNombreBlessesLegers(int nombreBlessesLegers) { this.nombreBlessesLegers = nombreBlessesLegers; }

    public int getNombreBlessesGraves() { return nombreBlessesGraves; }
    public void setNombreBlessesGraves(int nombreBlessesGraves) { this.nombreBlessesGraves = nombreBlessesGraves; }

    public int getNombreDeces() { return nombreDeces; }
    public void setNombreDeces(int nombreDeces) { this.nombreDeces = nombreDeces; }

    public boolean isAutresVehiculesImpliques() { return autresVehiculesImpliques; }
    public void setAutresVehiculesImpliques(boolean autresVehiculesImpliques) { this.autresVehiculesImpliques = autresVehiculesImpliques; }

    public int getNombreVehiculesImpliques() { return nombreVehiculesImpliques; }
    public void setNombreVehiculesImpliques(int nombreVehiculesImpliques) { this.nombreVehiculesImpliques = nombreVehiculesImpliques; }

    public String getDetailsAutresVehicules() { return detailsAutresVehicules; }
    public void setDetailsAutresVehicules(String detailsAutresVehicules) { this.detailsAutresVehicules = detailsAutresVehicules; }

    public String getDegatsVehiculeLoue() { return degatsVehiculeLoue; }
    public void setDegatsVehiculeLoue(String degatsVehiculeLoue) { this.degatsVehiculeLoue = degatsVehiculeLoue; }

    public String getDegatsAutresVehicules() { return degatsAutresVehicules; }
    public void setDegatsAutresVehicules(String degatsAutresVehicules) { this.degatsAutresVehicules = degatsAutresVehicules; }

    public String getDegatsInfrastructure() { return degatsInfrastructure; }
    public void setDegatsInfrastructure(String degatsInfrastructure) { this.degatsInfrastructure = degatsInfrastructure; }

    public Double getEstimationDegats() { return estimationDegats; }
    public void setEstimationDegats(Double estimationDegats) { this.estimationDegats = estimationDegats; }

    // Getters et setters manquants (partie 2)
    public String getNumeroConstatAmiable() { return numeroConstatAmiable; }
    public void setNumeroConstatAmiable(String numeroConstatAmiable) { this.numeroConstatAmiable = numeroConstatAmiable; }

    public String getNumeroDeclarationPolice() { return numeroDeclarationPolice; }
    public void setNumeroDeclarationPolice(String numeroDeclarationPolice) { this.numeroDeclarationPolice = numeroDeclarationPolice; }

    public String getCommissariatCompetent() { return commissariatCompetent; }
    public void setCommissariatCompetent(String commissariatCompetent) { this.commissariatCompetent = commissariatCompetent; }

    public String getNumeroProcessVerbal() { return numeroProcessVerbal; }
    public void setNumeroProcessVerbal(String numeroProcessVerbal) { this.numeroProcessVerbal = numeroProcessVerbal; }

    public boolean isAlcoolemieEffectuee() { return alcoolemieEffectuee; }
    public void setAlcoolemieEffectuee(boolean alcoolemieEffectuee) { this.alcoolemieEffectuee = alcoolemieEffectuee; }

    public String getResultatAlcoolemie() { return resultatAlcoolemie; }
    public void setResultatAlcoolemie(String resultatAlcoolemie) { this.resultatAlcoolemie = resultatAlcoolemie; }

    public String getCompagnieAssurance() { return compagnieAssurance; }
    public void setCompagnieAssurance(String compagnieAssurance) { this.compagnieAssurance = compagnieAssurance; }

    public String getNumeroPoliceAssurance() { return numeroPoliceAssurance; }
    public void setNumeroPoliceAssurance(String numeroPoliceAssurance) { this.numeroPoliceAssurance = numeroPoliceAssurance; }

    public String getNumeroDeclarationAssurance() { return numeroDeclarationAssurance; }
    public void setNumeroDeclarationAssurance(String numeroDeclarationAssurance) { this.numeroDeclarationAssurance = numeroDeclarationAssurance; }

    public LocalDate getDateDeclarationAssurance() { return dateDeclarationAssurance; }
    public void setDateDeclarationAssurance(LocalDate dateDeclarationAssurance) { this.dateDeclarationAssurance = dateDeclarationAssurance; }

    public String getNumeroSinistre() { return numeroSinistre; }
    public void setNumeroSinistre(String numeroSinistre) { this.numeroSinistre = numeroSinistre; }

    public boolean isPriseEnChargeAssurance() { return priseEnChargeAssurance; }
    public void setPriseEnChargeAssurance(boolean priseEnChargeAssurance) { this.priseEnChargeAssurance = priseEnChargeAssurance; }

    public boolean isTemoinsPresents() { return temoinsPresents; }
    public void setTemoinsPresents(boolean temoinsPresents) { this.temoinsPresents = temoinsPresents; }

    public String getDetailsTemoin1() { return detailsTemoin1; }
    public void setDetailsTemoin1(String detailsTemoin1) { this.detailsTemoin1 = detailsTemoin1; }

    public String getDetailsTemoin2() { return detailsTemoin2; }
    public void setDetailsTemoin2(String detailsTemoin2) { this.detailsTemoin2 = detailsTemoin2; }

    public String getDetailsTemoin3() { return detailsTemoin3; }
    public void setDetailsTemoin3(String detailsTemoin3) { this.detailsTemoin3 = detailsTemoin3; }

    public String getPhotosAccident() { return photosAccident; }
    public void setPhotosAccident(String photosAccident) { this.photosAccident = photosAccident; }

    public String getRapportPolice() { return rapportPolice; }
    public void setRapportPolice(String rapportPolice) { this.rapportPolice = rapportPolice; }

    public String getRapportExpertise() { return rapportExpertise; }
    public void setRapportExpertise(String rapportExpertise) { this.rapportExpertise = rapportExpertise; }

    public String getCertificatsMedicaux() { return certificatsMedicaux; }
    public void setCertificatsMedicaux(String certificatsMedicaux) { this.certificatsMedicaux = certificatsMedicaux; }

    public String getAutresDocuments() { return autresDocuments; }
    public void setAutresDocuments(String autresDocuments) { this.autresDocuments = autresDocuments; }

    public StatutAccident getStatut() { return statut; }
    public void setStatut(StatutAccident statut) {
        this.statut = statut;
        this.updatedAt = LocalDateTime.now();
    }

    public Double getMontantIndemnisation() { return montantIndemnisation; }
    public void setMontantIndemnisation(Double montantIndemnisation) { this.montantIndemnisation = montantIndemnisation; }

    public Double getMontantFranchise() { return montantFranchise; }
    public void setMontantFranchise(Double montantFranchise) { this.montantFranchise = montantFranchise; }

    public Double getMontantRecupere() { return montantRecupere; }
    public void setMontantRecupere(Double montantRecupere) { this.montantRecupere = montantRecupere; }

    public boolean isVehiculeReparable() { return vehiculeReparable; }
    public void setVehiculeReparable(boolean vehiculeReparable) { this.vehiculeReparable = vehiculeReparable; }

    public boolean isVehiculeEconomiquementIrreparable() { return vehiculeEconomiquementIrreparable; }
    public void setVehiculeEconomiquementIrreparable(boolean vehiculeEconomiquementIrreparable) { this.vehiculeEconomiquementIrreparable = vehiculeEconomiquementIrreparable; }

    public LocalDate getDateRemiseEnService() { return dateRemiseEnService; }
    public void setDateRemiseEnService(LocalDate dateRemiseEnService) { this.dateRemiseEnService = dateRemiseEnService; }

    public Double getCoutReparation() { return coutReparation; }
    public void setCoutReparation(Double coutReparation) { this.coutReparation = coutReparation; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    public String getAgentDeclarant() { return agentDeclarant; }
    public void setAgentDeclarant(String agentDeclarant) { this.agentDeclarant = agentDeclarant; }

    public String getDerniereModificationPar() { return derniereModificationPar; }
    public void setDerniereModificationPar(String derniereModificationPar) { this.derniereModificationPar = derniereModificationPar; }

    // Méthodes utilitaires
    public boolean estGrave() {
        return gravite == GraviteAccident.GRAVE ||
               gravite == GraviteAccident.TRES_GRAVE ||
               gravite == GraviteAccident.MORTEL;
    }

    public boolean necessiteDeclarationPolice() {
        return estGrave() || blessesClient || blessesTiers || nombreDeces > 0;
    }

    public boolean estClientResponsable() {
        return responsabilite == Responsabilite.CLIENT_RESPONSABLE;
    }

    public int getTotalBlesses() {
        return nombreBlessesLegers + nombreBlessesGraves;
    }

    public void declareALAssurance(String numeroDeclaration, String numeroSinistre) {
        this.numeroDeclarationAssurance = numeroDeclaration;
        this.numeroSinistre = numeroSinistre;
        this.dateDeclarationAssurance = LocalDate.now();
        this.updatedAt = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return String.format("Accident[id=%d, vehicule=%s, date=%s, type=%s, gravité=%s]",
                           id,
                           vehicule != null ? vehicule.getImmatriculation() : null,
                           dateAccident,
                           typeAccident,
                           gravite);
    }
}
