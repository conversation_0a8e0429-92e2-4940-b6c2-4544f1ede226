package model;

import jakarta.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Entité Prolongation - Gestion des extensions de location
 * Conforme à la réglementation marocaine des entreprises de location de voitures
 */
@Entity
@Table(name = "prolongation")
public class Prolongation {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "location_id", nullable = false)
    private Location location;

    // Dates de prolongation
    private LocalDate ancienneDateFin; // Ancienne date de fin
    private LocalDate nouvelleDateFin; // Nouvelle date de fin
    private LocalDate dateDemandeProlong; // Date de demande de prolongation
    private LocalDate dateApprouvee; // Date d'approbation

    // Détails financiers
    private double prixProlong; // Prix de la prolongation
    private double cautionSupplementaire; // Caution supplémentaire si nécessaire
    private double penaliteRetard; // Pénalité en cas de retard dans la demande

    // Statut de la prolongation
    public enum StatutProlong {
        DEMANDEE,      // Demande soumise
        APPROUVEE,     // Approuvée par l'agent/admin
        REFUSEE,       // Refusée
        ANNULEE,       // Annulée par le client
        TERMINEE       // Prolongation terminée
    }

    @Enumerated(EnumType.STRING)
    private StatutProlong statut;

    // Informations administratives
    private String motifProlong; // Motif de la prolongation
    private String notesAgent; // Notes de l'agent
    private String agentApprouveur; // Agent qui a approuvé
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Vérifications réglementaires marocaines
    private boolean verificationPermisValide; // Vérification validité permis
    private boolean verificationAssuranceValide; // Vérification assurance
    private boolean controleVehicule; // Contrôle état du véhicule
    private Integer kilometrageActuel; // Kilométrage au moment de la prolongation

    // Constructeurs
    public Prolongation() {
        this.createdAt = LocalDateTime.now();
        this.statut = StatutProlong.DEMANDEE;
    }

    public Prolongation(Location location, LocalDate nouvelleDateFin, String motif) {
        this();
        this.location = location;
        this.ancienneDateFin = location.getDateFinPrevue();
        this.nouvelleDateFin = nouvelleDateFin;
        this.motifProlong = motif;
        this.dateDemandeProlong = LocalDate.now();
    }

    // Getters et setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Location getLocation() { return location; }
    public void setLocation(Location location) { this.location = location; }

    public LocalDate getAncienneDateFin() { return ancienneDateFin; }
    public void setAncienneDateFin(LocalDate ancienneDateFin) { this.ancienneDateFin = ancienneDateFin; }

    public LocalDate getNouvelleDateFin() { return nouvelleDateFin; }
    public void setNouvelleDateFin(LocalDate nouvelleDateFin) { this.nouvelleDateFin = nouvelleDateFin; }

    public LocalDate getDateDemandeProlong() { return dateDemandeProlong; }
    public void setDateDemandeProlong(LocalDate dateDemandeProlong) { this.dateDemandeProlong = dateDemandeProlong; }

    public LocalDate getDateApprouvee() { return dateApprouvee; }
    public void setDateApprouvee(LocalDate dateApprouvee) { this.dateApprouvee = dateApprouvee; }

    public double getPrixProlong() { return prixProlong; }
    public void setPrixProlong(double prixProlong) { this.prixProlong = prixProlong; }

    public double getCautionSupplementaire() { return cautionSupplementaire; }
    public void setCautionSupplementaire(double cautionSupplementaire) { this.cautionSupplementaire = cautionSupplementaire; }

    public double getPenaliteRetard() { return penaliteRetard; }
    public void setPenaliteRetard(double penaliteRetard) { this.penaliteRetard = penaliteRetard; }

    public StatutProlong getStatut() { return statut; }
    public void setStatut(StatutProlong statut) { 
        this.statut = statut; 
        this.updatedAt = LocalDateTime.now();
    }

    public String getMotifProlong() { return motifProlong; }
    public void setMotifProlong(String motifProlong) { this.motifProlong = motifProlong; }

    public String getNotesAgent() { return notesAgent; }
    public void setNotesAgent(String notesAgent) { this.notesAgent = notesAgent; }

    public String getAgentApprouveur() { return agentApprouveur; }
    public void setAgentApprouveur(String agentApprouveur) { this.agentApprouveur = agentApprouveur; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    public boolean isVerificationPermisValide() { return verificationPermisValide; }
    public void setVerificationPermisValide(boolean verificationPermisValide) { this.verificationPermisValide = verificationPermisValide; }

    public boolean isVerificationAssuranceValide() { return verificationAssuranceValide; }
    public void setVerificationAssuranceValide(boolean verificationAssuranceValide) { this.verificationAssuranceValide = verificationAssuranceValide; }

    public boolean isControleVehicule() { return controleVehicule; }
    public void setControleVehicule(boolean controleVehicule) { this.controleVehicule = controleVehicule; }

    public Integer getKilometrageActuel() { return kilometrageActuel; }
    public void setKilometrageActuel(Integer kilometrageActuel) { this.kilometrageActuel = kilometrageActuel; }

    // Méthodes utilitaires
    public long getNombreJoursProlong() {
        if (ancienneDateFin != null && nouvelleDateFin != null) {
            return nouvelleDateFin.toEpochDay() - ancienneDateFin.toEpochDay();
        }
        return 0;
    }

    public double calculerPrixProlong(double prixParJour) {
        return getNombreJoursProlong() * prixParJour;
    }

    public boolean estEnRetard() {
        return dateDemandeProlong != null && ancienneDateFin != null && 
               dateDemandeProlong.isAfter(ancienneDateFin);
    }

    /**
     * Approuve la prolongation avec vérifications réglementaires
     */
    public void approuver(String agentNom) {
        if (verificationPermisValide && verificationAssuranceValide && controleVehicule) {
            this.statut = StatutProlong.APPROUVEE;
            this.agentApprouveur = agentNom;
            this.dateApprouvee = LocalDate.now();
            this.updatedAt = LocalDateTime.now();
            
            // Mettre à jour la location principale
            if (location != null) {
                location.setDateFinPrevue(nouvelleDateFin);
                location.setStatus(Location.Status.PROLONGE);
                location.setUpdatedAt(LocalDateTime.now());
            }
        } else {
            throw new IllegalStateException("Impossible d'approuver: vérifications réglementaires non complètes");
        }
    }

    @Override
    public String toString() {
        return String.format("Prolongation[id=%d, location=%d, du %s au %s, statut=%s]", 
                           id, location != null ? location.getId() : null, 
                           ancienneDateFin, nouvelleDateFin, statut);
    }
}
