package model;

import jakarta.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Entité Maintenance - Gestion de la maintenance des véhicules
 * Conforme aux standards de maintenance des flottes au Maroc
 */
@Entity
@Table(name = "maintenance")
public class Maintenance {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "vehicule_id", nullable = false)
    private Vehicule vehicule;

    // Type de maintenance
    public enum TypeMaintenance {
        PREVENTIVE,         // Maintenance préventive programmée
        CORRECTIVE,         // Maintenance corrective (panne)
        CONTROLE_TECHNIQUE, // Contrôle technique obligatoire
        VIDANGE,           // Vidange moteur
        REVISION,          // Révision complète
        REPARATION,        // Réparation spécifique
        CHANGEMENT_PNEUS,  // Changement de pneus
        FREINAGE,          // Système de freinage
        CLIMATISATION,     // Système de climatisation
        CARROSSERIE,       // Travaux de carrosserie
        ELECTRICITE,       // Système électrique
        URGENCE            // Maintenance d'urgence
    }

    @Enumerated(EnumType.STRING)
    private TypeMaintenance typeMaintenance;

    // Statut de la maintenance
    public enum StatutMaintenance {
        PROGRAMMEE,    // Programmée
        EN_COURS,      // En cours
        TERMINEE,      // Terminée
        ANNULEE,       // Annulée
        REPORTEE,      // Reportée
        EN_ATTENTE_PIECES // En attente de pièces
    }

    @Enumerated(EnumType.STRING)
    private StatutMaintenance statut;

    // Dates importantes
    private LocalDate dateProgrammee;
    private LocalDate dateDebut;
    private LocalDate dateFin;
    private LocalDate prochaineMaintenance; // Date de la prochaine maintenance

    // Informations kilométrage
    private Integer kilometrageActuel;
    private Integer prochainKilometrageMaintenance;
    private Integer intervalleKilometrage; // Intervalle en km pour la prochaine maintenance

    // Détails de la maintenance
    private String description;
    private String travauEffectues; // Détail des travaux effectués
    private String piecesChangees; // Liste des pièces changées
    private String observationsAvant; // Observations avant maintenance
    private String observationsApres; // Observations après maintenance

    // Informations financières
    private Double coutMainOeuvre; // Coût main d'œuvre
    private Double coutPieces; // Coût des pièces
    private Double coutTotal; // Coût total
    private String numeroFacture;
    private boolean facturePayee;

    // Informations fournisseur/garage
    private String nomGarage;
    private String adresseGarage;
    private String telephoneGarage;
    private String technicienResponsable;
    private String numeroOrdreReparation;

    // Garantie et conformité
    private boolean sousGarantie;
    private LocalDate finGarantie;
    private String numeroGarantie;
    private boolean conformiteReglementaire; // Conformité aux normes marocaines

    // Documents et certifications
    private String documentsJoints; // Chemins vers les documents
    private String certificatConformite; // Certificat de conformité
    private String rapportTechnique; // Rapport technique détaillé

    // Priorité et urgence
    public enum NiveauPriorite {
        FAIBLE, NORMALE, ELEVEE, CRITIQUE, URGENTE
    }

    @Enumerated(EnumType.STRING)
    private NiveauPriorite priorite;

    // Impact sur la disponibilité
    private boolean vehiculeImmobilise; // Véhicule immobilisé pendant maintenance
    private Integer dureeEstimeeJours; // Durée estimée en jours
    private Integer dureeReelleJours; // Durée réelle en jours

    // Métadonnées
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String createdBy; // Agent qui a créé la maintenance
    private String updatedBy; // Dernier agent à avoir modifié

    // Constructeurs
    public Maintenance() {
        this.createdAt = LocalDateTime.now();
        this.statut = StatutMaintenance.PROGRAMMEE;
        this.priorite = NiveauPriorite.NORMALE;
    }

    public Maintenance(Vehicule vehicule, TypeMaintenance type, String description) {
        this();
        this.vehicule = vehicule;
        this.typeMaintenance = type;
        this.description = description;
        this.kilometrageActuel = vehicule.getMetrage();
    }

    // Getters et setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Vehicule getVehicule() { return vehicule; }
    public void setVehicule(Vehicule vehicule) { this.vehicule = vehicule; }

    public TypeMaintenance getTypeMaintenance() { return typeMaintenance; }
    public void setTypeMaintenance(TypeMaintenance typeMaintenance) { this.typeMaintenance = typeMaintenance; }

    public StatutMaintenance getStatut() { return statut; }
    public void setStatut(StatutMaintenance statut) { 
        this.statut = statut; 
        this.updatedAt = LocalDateTime.now();
    }

    public LocalDate getDateProgrammee() { return dateProgrammee; }
    public void setDateProgrammee(LocalDate dateProgrammee) { this.dateProgrammee = dateProgrammee; }

    public LocalDate getDateDebut() { return dateDebut; }
    public void setDateDebut(LocalDate dateDebut) { this.dateDebut = dateDebut; }

    public LocalDate getDateFin() { return dateFin; }
    public void setDateFin(LocalDate dateFin) { 
        this.dateFin = dateFin;
        if (dateDebut != null && dateFin != null) {
            this.dureeReelleJours = (int) (dateFin.toEpochDay() - dateDebut.toEpochDay());
        }
    }

    public LocalDate getProchaineMaintenance() { return prochaineMaintenance; }
    public void setProchaineMaintenance(LocalDate prochaineMaintenance) { this.prochaineMaintenance = prochaineMaintenance; }

    public Integer getKilometrageActuel() { return kilometrageActuel; }
    public void setKilometrageActuel(Integer kilometrageActuel) { this.kilometrageActuel = kilometrageActuel; }

    public Integer getProchainKilometrageMaintenance() { return prochainKilometrageMaintenance; }
    public void setProchainKilometrageMaintenance(Integer prochainKilometrageMaintenance) { this.prochainKilometrageMaintenance = prochainKilometrageMaintenance; }

    public Integer getIntervalleKilometrage() { return intervalleKilometrage; }
    public void setIntervalleKilometrage(Integer intervalleKilometrage) { this.intervalleKilometrage = intervalleKilometrage; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public String getTravauEffectues() { return travauEffectues; }
    public void setTravauEffectues(String travauEffectues) { this.travauEffectues = travauEffectues; }

    public String getPiecesChangees() { return piecesChangees; }
    public void setPiecesChangees(String piecesChangees) { this.piecesChangees = piecesChangees; }

    public String getObservationsAvant() { return observationsAvant; }
    public void setObservationsAvant(String observationsAvant) { this.observationsAvant = observationsAvant; }

    public String getObservationsApres() { return observationsApres; }
    public void setObservationsApres(String observationsApres) { this.observationsApres = observationsApres; }

    public Double getCoutMainOeuvre() { return coutMainOeuvre; }
    public void setCoutMainOeuvre(Double coutMainOeuvre) { 
        this.coutMainOeuvre = coutMainOeuvre;
        calculerCoutTotal();
    }

    public Double getCoutPieces() { return coutPieces; }
    public void setCoutPieces(Double coutPieces) { 
        this.coutPieces = coutPieces;
        calculerCoutTotal();
    }

    public Double getCoutTotal() { return coutTotal; }
    public void setCoutTotal(Double coutTotal) { this.coutTotal = coutTotal; }

    public String getNumeroFacture() { return numeroFacture; }
    public void setNumeroFacture(String numeroFacture) { this.numeroFacture = numeroFacture; }

    public boolean isFacturePayee() { return facturePayee; }
    public void setFacturePayee(boolean facturePayee) { this.facturePayee = facturePayee; }

    public String getNomGarage() { return nomGarage; }
    public void setNomGarage(String nomGarage) { this.nomGarage = nomGarage; }

    public String getAdresseGarage() { return adresseGarage; }
    public void setAdresseGarage(String adresseGarage) { this.adresseGarage = adresseGarage; }

    public String getTelephoneGarage() { return telephoneGarage; }
    public void setTelephoneGarage(String telephoneGarage) { this.telephoneGarage = telephoneGarage; }

    public String getTechnicienResponsable() { return technicienResponsable; }
    public void setTechnicienResponsable(String technicienResponsable) { this.technicienResponsable = technicienResponsable; }

    public String getNumeroOrdreReparation() { return numeroOrdreReparation; }
    public void setNumeroOrdreReparation(String numeroOrdreReparation) { this.numeroOrdreReparation = numeroOrdreReparation; }

    public boolean isSousGarantie() { return sousGarantie; }
    public void setSousGarantie(boolean sousGarantie) { this.sousGarantie = sousGarantie; }

    public LocalDate getFinGarantie() { return finGarantie; }
    public void setFinGarantie(LocalDate finGarantie) { this.finGarantie = finGarantie; }

    public String getNumeroGarantie() { return numeroGarantie; }
    public void setNumeroGarantie(String numeroGarantie) { this.numeroGarantie = numeroGarantie; }

    public boolean isConformiteReglementaire() { return conformiteReglementaire; }
    public void setConformiteReglementaire(boolean conformiteReglementaire) { this.conformiteReglementaire = conformiteReglementaire; }

    public String getDocumentsJoints() { return documentsJoints; }
    public void setDocumentsJoints(String documentsJoints) { this.documentsJoints = documentsJoints; }

    public String getCertificatConformite() { return certificatConformite; }
    public void setCertificatConformite(String certificatConformite) { this.certificatConformite = certificatConformite; }

    public String getRapportTechnique() { return rapportTechnique; }
    public void setRapportTechnique(String rapportTechnique) { this.rapportTechnique = rapportTechnique; }

    public NiveauPriorite getPriorite() { return priorite; }
    public void setPriorite(NiveauPriorite priorite) { this.priorite = priorite; }

    public boolean isVehiculeImmobilise() { return vehiculeImmobilise; }
    public void setVehiculeImmobilise(boolean vehiculeImmobilise) { this.vehiculeImmobilise = vehiculeImmobilise; }

    public Integer getDureeEstimeeJours() { return dureeEstimeeJours; }
    public void setDureeEstimeeJours(Integer dureeEstimeeJours) { this.dureeEstimeeJours = dureeEstimeeJours; }

    public Integer getDureeReelleJours() { return dureeReelleJours; }
    public void setDureeReelleJours(Integer dureeReelleJours) { this.dureeReelleJours = dureeReelleJours; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }

    public String getUpdatedBy() { return updatedBy; }
    public void setUpdatedBy(String updatedBy) { this.updatedBy = updatedBy; }

    // Méthodes utilitaires
    private void calculerCoutTotal() {
        double mainOeuvre = coutMainOeuvre != null ? coutMainOeuvre : 0.0;
        double pieces = coutPieces != null ? coutPieces : 0.0;
        this.coutTotal = mainOeuvre + pieces;
    }

    public boolean estEnRetard() {
        return dateProgrammee != null && LocalDate.now().isAfter(dateProgrammee) && 
               statut != StatutMaintenance.TERMINEE;
    }

    public boolean estUrgente() {
        return priorite == NiveauPriorite.CRITIQUE || priorite == NiveauPriorite.URGENTE;
    }

    public void terminerMaintenance(String travaux, String pieces, String observations) {
        this.statut = StatutMaintenance.TERMINEE;
        this.dateFin = LocalDate.now();
        this.travauEffectues = travaux;
        this.piecesChangees = pieces;
        this.observationsApres = observations;
        this.updatedAt = LocalDateTime.now();
        
        // Calculer la prochaine maintenance si c'est préventive
        if (typeMaintenance == TypeMaintenance.PREVENTIVE && intervalleKilometrage != null) {
            this.prochainKilometrageMaintenance = kilometrageActuel + intervalleKilometrage;
        }
    }

    @Override
    public String toString() {
        return String.format("Maintenance[id=%d, vehicule=%s, type=%s, statut=%s, date=%s]", 
                           id, 
                           vehicule != null ? vehicule.getImmatriculation() : null, 
                           typeMaintenance, 
                           statut, 
                           dateProgrammee);
    }
}
