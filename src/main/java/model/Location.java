package model;

import jakarta.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Entity
public class Location {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    private Client client;

    @ManyToOne
    private Vehicule vehicule;

    private LocalDate dateDebut;
    private LocalDate dateFinPrevue;
    private LocalDate dateFinReelle;
    private double prixTotal;
    private double penalite;

    // Enhanced location fields
    private String contractNumber;
    private String pickupLocation;
    private String deliveryLocation;
    private String insuranceType; // Standard, Tous risques, Premium
    private String fuelPolicy; // Plein à plein, Plein à vide, Retour identique
    private double caution; // Security deposit
    private String notes;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Extra options
    private boolean optionGPS;
    private boolean optionSiegeEnfant;
    private boolean optionChauffeur;
    private boolean optionAssuranceComplete;
    private boolean optionKilometrageIllimite;
    private double prixOptions; // Total price for all options

    // Mileage tracking
    private Integer kilometrageDebut;
    private Integer kilometrageFin;
    private Integer kilometrageAutorise; // Allowed kilometers
    private double penaliteKilometrage; // Penalty for excess kilometers

    public enum Status {
        RESERVE, EN_COURS, TERMINE, ANNULE, PROLONGE
    }

    @Enumerated(EnumType.STRING)
    private Status status;

    // Relationship with prolongations
    @OneToMany(mappedBy = "location", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Prolongation> prolongations;

    // Basic getters and setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    public Client getClient() { return client; }
    public void setClient(Client client) { this.client = client; }
    public Vehicule getVehicule() { return vehicule; }
    public void setVehicule(Vehicule vehicule) { this.vehicule = vehicule; }
    public java.time.LocalDate getDateDebut() { return dateDebut; }
    public void setDateDebut(java.time.LocalDate dateDebut) { this.dateDebut = dateDebut; }
    public java.time.LocalDate getDateFinPrevue() { return dateFinPrevue; }
    public void setDateFinPrevue(java.time.LocalDate dateFinPrevue) { this.dateFinPrevue = dateFinPrevue; }
    public java.time.LocalDate getDateFinReelle() { return dateFinReelle; }
    public void setDateFinReelle(java.time.LocalDate dateFinReelle) { this.dateFinReelle = dateFinReelle; }
    public double getPrixTotal() { return prixTotal; }
    public void setPrixTotal(double prixTotal) { this.prixTotal = prixTotal; }
    public double getPenalite() { return penalite; }
    public void setPenalite(double penalite) { this.penalite = penalite; }

    public Status getStatus() { return status; }
    public void setStatus(Status status) { this.status = status; }

    // Getters et setters pour les champs étendus (Réglementation marocaine)
    public String getContractNumber() { return contractNumber; }
    public void setContractNumber(String contractNumber) { this.contractNumber = contractNumber; }
    public String getPickupLocation() { return pickupLocation; }
    public void setPickupLocation(String pickupLocation) { this.pickupLocation = pickupLocation; }
    public String getDeliveryLocation() { return deliveryLocation; }
    public void setDeliveryLocation(String deliveryLocation) { this.deliveryLocation = deliveryLocation; }
    public String getInsuranceType() { return insuranceType; }
    public void setInsuranceType(String insuranceType) { this.insuranceType = insuranceType; }
    public String getFuelPolicy() { return fuelPolicy; }
    public void setFuelPolicy(String fuelPolicy) { this.fuelPolicy = fuelPolicy; }
    public double getCaution() { return caution; }
    public void setCaution(double caution) { this.caution = caution; }
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    // Getters et setters pour les options supplémentaires
    public boolean isOptionGPS() { return optionGPS; }
    public void setOptionGPS(boolean optionGPS) { this.optionGPS = optionGPS; }
    public boolean isOptionSiegeEnfant() { return optionSiegeEnfant; }
    public void setOptionSiegeEnfant(boolean optionSiegeEnfant) { this.optionSiegeEnfant = optionSiegeEnfant; }
    public boolean isOptionChauffeur() { return optionChauffeur; }
    public void setOptionChauffeur(boolean optionChauffeur) { this.optionChauffeur = optionChauffeur; }
    public boolean isOptionAssuranceComplete() { return optionAssuranceComplete; }
    public void setOptionAssuranceComplete(boolean optionAssuranceComplete) { this.optionAssuranceComplete = optionAssuranceComplete; }
    public boolean isOptionKilometrageIllimite() { return optionKilometrageIllimite; }
    public void setOptionKilometrageIllimite(boolean optionKilometrageIllimite) { this.optionKilometrageIllimite = optionKilometrageIllimite; }
    public double getPrixOptions() { return prixOptions; }
    public void setPrixOptions(double prixOptions) { this.prixOptions = prixOptions; }

    // Getters et setters pour le kilométrage
    public Integer getKilometrageDebut() { return kilometrageDebut; }
    public void setKilometrageDebut(Integer kilometrageDebut) { this.kilometrageDebut = kilometrageDebut; }
    public Integer getKilometrageFin() { return kilometrageFin; }
    public void setKilometrageFin(Integer kilometrageFin) { this.kilometrageFin = kilometrageFin; }
    public Integer getKilometrageAutorise() { return kilometrageAutorise; }
    public void setKilometrageAutorise(Integer kilometrageAutorise) { this.kilometrageAutorise = kilometrageAutorise; }
    public double getPenaliteKilometrage() { return penaliteKilometrage; }
    public void setPenaliteKilometrage(double penaliteKilometrage) { this.penaliteKilometrage = penaliteKilometrage; }

    // Prolongations
    public List<Prolongation> getProlongations() { return prolongations; }
    public void setProlongations(List<Prolongation> prolongations) { this.prolongations = prolongations; }

    /**
     * Update the status based on current date and location dates.
     * Should be called after any date/status change.
     */
    public void updateStatus() {
        LocalDate today = LocalDate.now();
        if (status == Status.ANNULE) return; // Don't auto-update if cancelled
        if (dateDebut != null && dateFinPrevue != null) {
            if (today.isBefore(dateDebut)) {
                status = Status.RESERVE;
            } else if ((today.isEqual(dateDebut) || today.isAfter(dateDebut)) && (dateFinReelle == null || today.isBefore(dateFinReelle))) {
                status = Status.EN_COURS;
            } else if (dateFinReelle != null && today.isAfter(dateFinReelle)) {
                status = Status.TERMINE;
            } else if (today.isAfter(dateFinPrevue)) {
                status = Status.TERMINE;
            }
        }
    }
} 