package model;

import jakarta.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Entité HistoriqueVehicule - Suivi complet de l'historique des véhicules
 * Conforme aux exigences de traçabilité des entreprises de location marocaines
 */
@Entity
@Table(name = "historique_vehicule")
public class HistoriqueVehicule {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "vehicule_id", nullable = false)
    private Vehicule vehicule;

    // Type d'événement dans l'historique
    public enum TypeEvenement {
        ACQUISITION,        // Acquisition du véhicule
        LOCATION_DEBUT,     // Début de location
        LOCATION_FIN,       // Fin de location
        MAINTENANCE,        // Maintenance préventive/corrective
        ACCIDENT,          // Accident déclaré
        CONTROLE_TECHNIQUE, // Contrôle technique
        REPARATION,        // Réparation
        CHANGEMENT_ETAT,   // Changement d'état (disponible/loué/panne)
        MISE_HORS_SERVICE, // Mise hors service
        VENTE,             // Vente du véhicule
        VOL,               // Déclaration de vol
        SINISTRE           // Sinistre assurance
    }

    @Enumerated(EnumType.STRING)
    private TypeEvenement typeEvenement;

    // Informations de l'événement
    private LocalDateTime dateEvenement;
    private String description;
    private String detailsEvenement; // Détails complets de l'événement
    
    // Informations kilométrage
    private Integer kilometrageAvant;
    private Integer kilometrageApres;
    private Integer kilometrageParcouru;

    // Informations financières
    private Double coutEvenement; // Coût associé à l'événement
    private String numeroFacture; // Numéro de facture si applicable
    private String fournisseur; // Fournisseur/garage/assurance

    // Informations client (si applicable)
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "client_id")
    private Client client;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "location_id")
    private Location location;

    // Informations agent/responsable
    private String agentResponsable;
    private String numeroRapport; // Numéro de rapport officiel

    // Documents associés
    private String documentsJoints; // Chemins vers les documents (photos, rapports, etc.)
    private String numeroPoliceAssurance; // Numéro de police d'assurance si applicable

    // État du véhicule après l'événement
    private String etatVehiculeApres; // État après l'événement
    private String observationsAgent; // Observations de l'agent
    
    // Informations réglementaires marocaines
    private String numeroDeclarationPolice; // Numéro déclaration police (accidents)
    private String numeroConstatAmiable; // Numéro constat amiable
    private boolean declarationAssurance; // Déclaration faite à l'assurance
    private LocalDate dateDeclarationAssurance;

    // Métadonnées
    private LocalDateTime createdAt;
    private String createdBy;

    // Constructeurs
    public HistoriqueVehicule() {
        this.createdAt = LocalDateTime.now();
        this.dateEvenement = LocalDateTime.now();
    }

    public HistoriqueVehicule(Vehicule vehicule, TypeEvenement type, String description) {
        this();
        this.vehicule = vehicule;
        this.typeEvenement = type;
        this.description = description;
    }

    // Getters et setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Vehicule getVehicule() { return vehicule; }
    public void setVehicule(Vehicule vehicule) { this.vehicule = vehicule; }

    public TypeEvenement getTypeEvenement() { return typeEvenement; }
    public void setTypeEvenement(TypeEvenement typeEvenement) { this.typeEvenement = typeEvenement; }

    public LocalDateTime getDateEvenement() { return dateEvenement; }
    public void setDateEvenement(LocalDateTime dateEvenement) { this.dateEvenement = dateEvenement; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public String getDetailsEvenement() { return detailsEvenement; }
    public void setDetailsEvenement(String detailsEvenement) { this.detailsEvenement = detailsEvenement; }

    public Integer getKilometrageAvant() { return kilometrageAvant; }
    public void setKilometrageAvant(Integer kilometrageAvant) { this.kilometrageAvant = kilometrageAvant; }

    public Integer getKilometrageApres() { return kilometrageApres; }
    public void setKilometrageApres(Integer kilometrageApres) { 
        this.kilometrageApres = kilometrageApres;
        if (kilometrageAvant != null && kilometrageApres != null) {
            this.kilometrageParcouru = kilometrageApres - kilometrageAvant;
        }
    }

    public Integer getKilometrageParcouru() { return kilometrageParcouru; }
    public void setKilometrageParcouru(Integer kilometrageParcouru) { this.kilometrageParcouru = kilometrageParcouru; }

    public Double getCoutEvenement() { return coutEvenement; }
    public void setCoutEvenement(Double coutEvenement) { this.coutEvenement = coutEvenement; }

    public String getNumeroFacture() { return numeroFacture; }
    public void setNumeroFacture(String numeroFacture) { this.numeroFacture = numeroFacture; }

    public String getFournisseur() { return fournisseur; }
    public void setFournisseur(String fournisseur) { this.fournisseur = fournisseur; }

    public Client getClient() { return client; }
    public void setClient(Client client) { this.client = client; }

    public Location getLocation() { return location; }
    public void setLocation(Location location) { this.location = location; }

    public String getAgentResponsable() { return agentResponsable; }
    public void setAgentResponsable(String agentResponsable) { this.agentResponsable = agentResponsable; }

    public String getNumeroRapport() { return numeroRapport; }
    public void setNumeroRapport(String numeroRapport) { this.numeroRapport = numeroRapport; }

    public String getDocumentsJoints() { return documentsJoints; }
    public void setDocumentsJoints(String documentsJoints) { this.documentsJoints = documentsJoints; }

    public String getNumeroPoliceAssurance() { return numeroPoliceAssurance; }
    public void setNumeroPoliceAssurance(String numeroPoliceAssurance) { this.numeroPoliceAssurance = numeroPoliceAssurance; }

    public String getEtatVehiculeApres() { return etatVehiculeApres; }
    public void setEtatVehiculeApres(String etatVehiculeApres) { this.etatVehiculeApres = etatVehiculeApres; }

    public String getObservationsAgent() { return observationsAgent; }
    public void setObservationsAgent(String observationsAgent) { this.observationsAgent = observationsAgent; }

    public String getNumeroDeclarationPolice() { return numeroDeclarationPolice; }
    public void setNumeroDeclarationPolice(String numeroDeclarationPolice) { this.numeroDeclarationPolice = numeroDeclarationPolice; }

    public String getNumeroConstatAmiable() { return numeroConstatAmiable; }
    public void setNumeroConstatAmiable(String numeroConstatAmiable) { this.numeroConstatAmiable = numeroConstatAmiable; }

    public boolean isDeclarationAssurance() { return declarationAssurance; }
    public void setDeclarationAssurance(boolean declarationAssurance) { this.declarationAssurance = declarationAssurance; }

    public LocalDate getDateDeclarationAssurance() { return dateDeclarationAssurance; }
    public void setDateDeclarationAssurance(LocalDate dateDeclarationAssurance) { this.dateDeclarationAssurance = dateDeclarationAssurance; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }

    // Méthodes utilitaires
    public boolean estEvenementFinancier() {
        return coutEvenement != null && coutEvenement > 0;
    }

    public boolean necessiteDeclarationAssurance() {
        return typeEvenement == TypeEvenement.ACCIDENT || 
               typeEvenement == TypeEvenement.SINISTRE || 
               typeEvenement == TypeEvenement.VOL;
    }

    public String getResumeEvenement() {
        return String.format("%s - %s (%s)", 
                           typeEvenement.toString(), 
                           description, 
                           dateEvenement.toLocalDate().toString());
    }

    @Override
    public String toString() {
        return String.format("HistoriqueVehicule[id=%d, vehicule=%s, type=%s, date=%s]", 
                           id, 
                           vehicule != null ? vehicule.getImmatriculation() : null, 
                           typeEvenement, 
                           dateEvenement.toLocalDate());
    }
}
